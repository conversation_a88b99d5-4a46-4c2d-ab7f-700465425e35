package warning

import (
	"fmt"
	"gitee.com/liujinsuo/tool"
	"ms-version.soofun.online/wjl/game_public/types_public"
	"os"
	"os/exec"
	"snakes/common/logx"
	"snakes/conf"
	"strings"
	"time"
)

type WarningInfo map[string]interface{}

var bobiEnv types_public.BobiEnv
var feishuAddr string

func SetConfig(env int, feishu string) {
	bobiEnv = types_public.BobiEnv(env)
	//feishuAddr = feishu
	feishuAddr = "https://open.feishu.cn/open-apis/bot/v2/hook/cee1c54b-fad5-47c5-b7ca-3ce0765f4a62" // 写死即可，不走配置
}

func SendWarning(info WarningInfo) {
	host, _ := os.Hostname()

	strArray := make([]string, 0)
	strArray = append(strArray, fmt.Sprintf("主机名: %v", host))
	strArray = append(strArray, fmt.Sprintf("环境名: %v", bobiEnv.String()))
	strArray = append(strArray, fmt.Sprintf("进程名: %v", getProcessName()))
	strArray = append(strArray, fmt.Sprintf("游戏名: %v", conf.Conf.Server.Project))
	strArray = append(strArray, fmt.Sprintf("服务器: %v", conf.Conf.Server.ID))

	for key, value := range info {
		strArray = append(strArray, fmt.Sprintf("%v: %v", key, value))
	}

	nowTime := time.Now()
	strArray = append(strArray, fmt.Sprintf("时间戳: %v", nowTime.UnixMilli()))
	strArray = append(strArray, fmt.Sprintf("时间串: %v", nowTime.Format("2006-01-02 15:04:05")))

	content := strings.Join(strArray, "\n")
	if err := tool.FeiShu.SendTextMsg(feishuAddr, content); err != nil {
		logx.Errorf("发送飞书报警信息失败,飞书地址=%v", feishuAddr)
	}
}

func getProcessName() string {
	// 通过调用系统命令获取进程名称
	cmd := exec.Command("ps", "-p", fmt.Sprintf("%d", os.Getpid()), "-o", "comm=")
	output, err := cmd.Output()
	if err != nil {
		logx.Errorf("getProcessName 获取进程名失败,err=%v", err.Error())
		return ""
	}
	return strings.TrimSpace(string(output))
}
