package conf

import (
	"ms-version.soofun.online/wjl/game_public/types_public"
	"snakes/constvar"
)

type (
	// RoomConf 房间配置
	RoomConf struct {
		ID         constvar.RoomType `json:"id"`         // 房间类型
		Tax        float64           `json:"tax"`        // 税率
		Fees       []int             `json:"fees"`       // 入场费列表
		GridNums   []int             `json:"gridNums"`   // 方格数
		PlayerNums []int             `json:"playerNums"` // 玩家人数列表
	}

	PairWait struct {
		Min int `json:"min"` // 最小等待秒数
		Max int `json:"max"` // 最大等待秒数
	}

	// RobotConf 机器人配置
	RobotConf struct {
		PointWeights constvar.IntSlice `json:"pointWeights"` // 点数权重
		TrapRate     int               `json:"trapRate"`     // 触发陷阱概率，已放大100倍
		LadderRate   int               `json:"ladderRate"`   // 触发梯子概率，已放大100倍
	}

	// ProductConf 商品配置
	ProductConf struct {
		ID    constvar.ProductID `json:"id"`
		Price int                `json:"price"`
	}

	// VoiceConf 语聊房的默认配置
	VoiceConf struct {
		PlayerNum int  `json:"playerNum"` // 玩家人数
		Fee       int  `json:"fee"`       // 房间费
		GridNum   int  `json:"gridNum"`   // 方格数
		PropMode  int  `json:"propMode"`  // 0 无道具模式 1 有道具模式
		KickOut   bool `json:"kickOut"`   // true 允许踢人,false 不允许踢人
	}

	// ChannelConf 渠道配置
	ChannelConf struct {
		HideCoin       bool                   `json:"hideCoin"`       // 是否隐藏金币
		NeedRobot      bool                   `json:"needRobot"`      // 是否需要机器人
		GameReport     bool                   `json:"gameReport"`     // 是否游戏上报
		RobotCountries []types_public.Country `json:"robotCountries"` // 机器人国家
		RoomConfigs    []*RoomConf            `json:"roomConfigs"`    // 房间配置
		PairWait       PairWait               `json:"pairWait"`       // 匹配等待时间范围
		CoinType       int                    `json:"coinType"`       // 支持的货币类型,默认为0, 兼容老的GameCoin
		ExchangeRate   int64                  `json:"exchangeRate"`   // 该平台1美元可以买到的金币数量
		ExpectDollar   int64                  `json:"expectDollar"`   // 基础奖池数(单位美元)
		FusingDollar   int64                  `json:"fusingDollar"`   // 熔断值(单位美元)
		WinRankCount   int                    `json:"winRankCount"`   // 前N名有奖励
		EasyRobot      RobotConf              `json:"easyRobot"`      // 容易机器人配置
		HardRobot      RobotConf              `json:"hardRobot"`      // 困难机器人配置
		GamePlayer     RobotConf              `json:"gamePlayer"`     // 游戏玩家配置
		ProductConfigs []*ProductConf         `json:"productConfigs"` // 商品列表
		VoiceConfig    VoiceConf              `json:"voiceConfig"`    // 语聊房的默认配置
		ConfigNotice   string                 `json:"config_notice"`  // 语聊房配置改变上报的地址

		BaseJackpot int64 `json:"-"` // 基础奖池数(期望奖池数), ExpectDollar折算为奖池金币数量
		Fusing      int64 `json:"-"` // 熔断值, FusingDollar折算为奖池金币数量
	}
)

func (c *ChannelConf) GetRoomConf(id constvar.RoomType) *RoomConf {
	for _, v := range c.RoomConfigs {
		if v.ID == id {
			return v
		}
	}
	return nil
}

func (c *ChannelConf) GetProductConf(id constvar.ProductID) *ProductConf {
	for _, v := range c.ProductConfigs {
		if v.ID == id {
			return v
		}
	}
	return nil
}

func GetDefaultChCfg() *ChannelConf {
	// 正式环境，渠道配置默认免门票
	if Conf.Server.Env == 2 {
		for _, v := range ChannelConfig.RoomConfigs {
			v.Fees = []int{0}
		}
	}
	return ChannelConfig
}

var ChannelConfig = &ChannelConf{
	HideCoin:       false,
	NeedRobot:      true,
	RobotCountries: []types_public.Country{types_public.CountryUS},
	ExchangeRate:   1000,
	ExpectDollar:   100,
	FusingDollar:   100,
	CoinType:       0,
	PairWait:       PairWait{Min: 4, Max: 6},
	WinRankCount:   2,
	RoomConfigs: []*RoomConf{
		{ID: constvar.RoomTypeCommon, Tax: 0.05, Fees: []int{0, 500, 2000}, GridNums: []int{50, 100}, PlayerNums: []int{2, 3, 4}},
		{ID: constvar.RoomTypePrivate, Tax: 0.05, Fees: []int{0, 500, 2000}, GridNums: []int{50, 100}, PlayerNums: []int{2, 3, 4}},
		{ID: constvar.RoomTypeVoice, Tax: 0.05, Fees: []int{0, 500, 2000}, GridNums: []int{50, 100}, PlayerNums: []int{2, 3, 4}},
	},
	ProductConfigs: []*ProductConf{
		{ID: 2000, Price: 100},
		{ID: 2001, Price: 100},
		{ID: 2002, Price: 100},
		{ID: 2003, Price: 100},
		{ID: 2004, Price: 100},
		{ID: 2005, Price: 100},
		{ID: 2006, Price: 100},
	},
	EasyRobot: RobotConf{
		PointWeights: constvar.IntSlice{900, 900, 1000, 1100, 1100, 1000},
		TrapRate:     0,
		LadderRate:   0,
	},
	HardRobot: RobotConf{
		PointWeights: constvar.IntSlice{800, 900, 900, 1100, 1100, 1200},
		TrapRate:     8,
		LadderRate:   0,
	},
	GamePlayer: RobotConf{
		PointWeights: constvar.IntSlice{1000, 1000, 1000, 1000, 1000, 1000},
		TrapRate:     0,
		LadderRate:   0,
	},
	VoiceConfig: VoiceConf{
		PlayerNum: 4,
		Fee:       0,
		GridNum:   50,
		PropMode:  1,
		KickOut:   true,
	},
}
