package constvar

type Warning string

const (
	WarningRoomGameTimeLong   Warning = "room_game_time_long"   // 房间卡死：房间运行时长，超过最大游戏时长
	WarningRoomStatusTimeLong Warning = "room_status_time_long" // 房间卡死：房间状态，超时未推进
	WarningRoomQueueBacklog   Warning = "room_queue_backlog"    // 房间卡死：房间消息队列堆积
	WarningPanic              Warning = "panic"                 // 游戏遇到panic
	WarningSystemQueueBacklog Warning = "system_queue_backlog"  // 系统卡死：系统消息队列堆积
)

// Limit 每个游戏，设置适用自己的报警条件，如高频率通信游戏的队列长度
func (w Warning) Limit() int {
	switch w {
	case WarningRoomGameTimeLong: // 最大游戏时长(秒)，如55分钟
		return 55 * 60
	case WarningRoomStatusTimeLong: // 最大游戏状态时长(秒)，如2分钟
		return 2 * 60
	case WarningRoomQueueBacklog: // 最大房间消息队列长度，如900
		return 900
	case WarningSystemQueueBacklog: // 最大系统消息队列长度，如9000
		return 9000
	default:
		return 0
	}
}
