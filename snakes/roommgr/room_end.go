package roommgr

import (
	"github.com/shopspring/decimal"
	"ms-version.soofun.online/wjl/game_public/types_public"
	"snakes/common/convertx"
	"snakes/common/logx"
	"snakes/common/safe"
	"snakes/common/tools"
	"snakes/constvar"
	"snakes/ecode"
	"snakes/model/common/base"
	"snakes/model/common/response"
	"sort"
	"time"
)

// SetSettlement 游戏大结算
func (slf *Room) SetSettlement() {
	defer func() {
		if slf.voiceRoom != nil {
			slf.voiceRoom.NoticeGameEnd()
		}
	}()

	// 定先手游戏状态，游戏强制提前结算(需要初始化slf.allPlayerInfo)
	if slf.gameStatus == constvar.GameStatusFirstMove {
		logx.Infof("RoomID:%v SetSettlement gameStatus firstMove", slf.RoomID)
		var sitUsers []*RoomUser
		for _, v := range slf.allUser {
			if v.Pos < 0 {
				continue
			}
			sitUsers = append(sitUsers, v)
		}
		if len(sitUsers) == slf.playerNum {
			for i := 0; i < slf.playerNum; i++ {
				slf.allPlayerInfo[i].SetUser(sitUsers[i])
			}
		}
	}

	slf.isSettled = true
	var rankPlayerList []*PlayerInfo
	for _, v := range slf.allPlayerInfo {
		rankPlayerList = append(rankPlayerList, v)
	}

	// 根据棋子位置排名
	sort.Slice(rankPlayerList, func(i, j int) bool {
		return rankPlayerList[i].CurChessPos > rankPlayerList[j].CurChessPos
	})

	var lastChessPos = -1
	var rankCountMap = make(map[int][]*PlayerInfo) // 排名 => 排名的人员列表
	for i, v := range rankPlayerList {
		if v.CurChessPos == lastChessPos {
			v.SettleRank = rankPlayerList[i-1].SettleRank
			rankCountMap[v.SettleRank] = append(rankCountMap[v.SettleRank], v)
			continue
		}

		v.SettleRank = i + 1
		lastChessPos = v.CurChessPos
		rankCountMap[v.SettleRank] = append(rankCountMap[v.SettleRank], v)
	}

	var tax = decimal.NewFromFloat(slf.roomConfig.Tax).Mul(decimal.NewFromInt(int64(slf.fee))).IntPart() * int64(slf.playerNum) // 抽水
	var totalCoin = int64(slf.fee * slf.playerNum)
	var leftCoin = totalCoin - tax
	if slf.fee > 0 {
		switch slf.playerNum {
		case 2:
			// 第一名平分
			rankFirstList := rankCountMap[1]
			if len(rankFirstList) < 1 {
				logx.Infof("RoomID:%v SetSettlement rankFirst no find", slf.RoomID)
				return
			}
			var firstCoin = decimal.NewFromInt(leftCoin).Div(decimal.NewFromInt(int64(len(rankFirstList)))).IntPart()
			for _, v := range rankFirstList {
				v.CoinChg = firstCoin
			}
		case 3, 4:
			rankFirstList := rankCountMap[1]
			if len(rankFirstList) < 1 {
				logx.Infof("RoomID:%v SetSettlement rankFirst no find", slf.RoomID)
				return
			}

			if slf.channelCfg.WinRankCount == 1 {
				// 第一名平分
				var firstCoin = decimal.NewFromInt(leftCoin).Div(decimal.NewFromInt(int64(len(rankFirstList)))).IntPart()
				for _, v := range rankFirstList {
					v.CoinChg = firstCoin
				}
			} else {
				// 第一名奖池60%，剩余的是第二名奖池
				if len(rankFirstList) >= 2 {
					// 第一名大于等于2个人，平分第一名奖池+第二名奖池之和
					var firstCoin = decimal.NewFromInt(leftCoin).Div(decimal.NewFromInt(int64(len(rankFirstList)))).IntPart()
					for _, v := range rankFirstList {
						v.CoinChg = firstCoin
					}
				} else {
					// 第一名一个人，第一名拿走60%
					rankFirstList[0].CoinChg = decimal.NewFromInt(totalCoin).Mul(decimal.NewFromFloat(0.6)).IntPart()
					rankSecondList := rankCountMap[2]
					if len(rankSecondList) <= 0 {
						logx.Infof("RoomID:%v SetSettlement rankSecond no find", slf.RoomID)
					} else {
						// 第二名平分剩余的部分
						var secondTotalCoin = leftCoin - rankFirstList[0].CoinChg
						var secondCoin = decimal.NewFromInt(secondTotalCoin).Div(decimal.NewFromInt(int64(len(rankSecondList)))).IntPart()
						for _, v := range rankSecondList {
							v.CoinChg = secondCoin
						}
					}
				}
			}
		default:
			logx.Infof("RoomID:%v SetSettlement playerNum:%v err", slf.RoomID, slf.playerNum)
			return
		}
	}

	// 结算金币变化(免费场也进行统计吧)
	for _, v := range slf.allPlayerInfo {
		// 抽水的变化记录，挂到第一名的金币变化记录上
		var gameTax int64
		if v.SettleRank == 1 {
			gameTax = tax
		}
		_, _ = slf.ChangeBalance(v.UserID, int(types_public.ActionEventTwo), v.CoinChg, "settle", "settle", constvar.CoinChangeTypeSettle, int(gameTax), v.SettleRank)
	}

	// 通知大结算结果
	var userSettleList []*response.UserSettlement
	for _, v := range rankPlayerList {
		userSettleList = append(userSettleList, &response.UserSettlement{
			UserID:      v.UserID,
			CoinChg:     v.CoinChg,
			Coin:        slf.GetUserCoin(v.UserID),
			Rank:        v.SettleRank,
			CurChessPos: v.CurChessPos,
		})
	}
	var notice = &response.NoticeSettlement{
		ApiScene: slf.apiScene,
		Users:    userSettleList,
	}
	slf.Broadcast(constvar.MsgTypeSettlement, ecode.OK, notice)
	logx.Infof("RoomID:%v Settlement success notice:%+v", slf.RoomID, tools.GetObj(notice))

	// 上报游戏结束信息
	if slf.voiceRoom != nil || slf.channelCfg.GameReport {
		var extend = &base.ReportExtend{SettleType: slf.settleType, Extend: slf.apiCreateExtend}
		for _, v := range slf.allPlayerInfo {
			item := &base.ReportUser{
				UserID: v.UserID,
			}
			user := slf.GetUser(v.UserID)
			if user != nil {
				item.IsOffline = user.IsOffline
				item.IsLeave = user.IsLeave
			}
			extend.Users = append(extend.Users, item)
		}

		endInfoList := make([]base.GameEndInfo, 0)
		for _, v := range userSettleList {
			endInfo := base.GameEndInfo{
				UserID:  v.UserID,
				SSToken: "",
				IsAI:    0,
				Rank:    v.Rank,
				Reward:  int(v.CoinChg),
				Score:   v.CurChessPos,
			}
			user := slf.GetUser(v.UserID)
			if user != nil {
				endInfo.SSToken = user.SSToken
				endInfo.IsAI = convertx.BoolToInt(user.IsRobot())
			}
			endInfoList = append(endInfoList, endInfo)
		}
		slf.ReportGameSettle(slf.CreateTime.UnixMilli(), time.Now().UnixMilli(), slf.GetRoundID(), endInfoList, extend)
	}
}

// ForceCloseRoom 强制关闭房间
func (slf *Room) ForceCloseRoom() {
	if slf.IsRefuseService() {
		logx.Infof("RoomID:%v ForceCloseRoom have refuseService", slf.RoomID)
		return
	}
	logx.Infof("RoomID:%v ForceCloseRoom begin, gameStatus:%v", slf.RoomID, slf.gameStatus)
	slf.SetRefuseService() // 标记房间拒绝服务

	// 打印房间
	slf.PrintRoom()

	// 游戏中，归还房间费
	if slf.gameStatus > 0 {
		// 定先手游戏状态，游戏强制提前结算(需要初始化slf.allPlayerInfo)
		if slf.gameStatus == constvar.GameStatusFirstMove {
			var sitUsers []*RoomUser
			for _, v := range slf.allUser {
				if v.Pos < 0 {
					continue
				}
				sitUsers = append(sitUsers, v)
			}
			if len(sitUsers) == slf.playerNum {
				for i := 0; i < slf.playerNum; i++ {
					slf.allPlayerInfo[i].SetUser(sitUsers[i])
				}
			}
		}
		for pos := 0; pos < slf.playerNum; pos++ {
			if len(slf.allPlayerInfo[pos].UserID) <= 0 || slf.fee <= 0 {
				continue
			}

			var coinChg = int64(slf.fee) // 房间费
			_, errCode := slf.ChangeBalance(slf.allPlayerInfo[pos].UserID, int(types_public.ActionEventTwo), coinChg, "settle", "settle", constvar.CoinChangeTypeForceCloseRoom, 0, 0)
			if errCode != ecode.OK {
				logx.Errorf("RoomID:%v userID:%v ForceCloseRoom ChangeBalance failed, coinChg:%v, errCode:%v", slf.RoomID, slf.allPlayerInfo[pos].UserID, coinChg, errCode)
			}
		}

		// 上报游戏结束信息
		if slf.voiceRoom != nil {
			slf.voiceRoom.NoticeGameEnd()
			// todo 没有上报游戏结束
		}
	}

	// 移除所有玩家
	var allUser = slf.GetAllUser()
	for _, v := range allUser {
		slf.RemoveUser(v.UserID)
	}
	slf.gameStatus = 0 // 因为RemoveRoom是异步的，所以需要把状态置为0，这样定时器就状态检查就不执行了

	// 移除房间
	safe.Go(func() {
		GetInstance().RemoveRoom(slf.RoomID)
		logx.Infof("RoomID:%v ForceCloseRoom success", slf.RoomID)
	})
}
