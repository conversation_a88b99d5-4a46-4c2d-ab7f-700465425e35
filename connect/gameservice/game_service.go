package gameservice

import (
	"connect/common/convertx"
	"connect/common/httpx"
	"connect/common/logx"
	"connect/common/safe"
	"connect/conf"
	"connect/ecode"
	"connect/model"
	"connect/model/common/request"
	"connect/model/common/response"
	"connect/model/dao"
	"connect/usermgr"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/go-redis/redis/v8"
	"github.com/go-resty/resty/v2"
	"golang.org/x/sync/errgroup"
	"golang.org/x/sync/singleflight"
	"sort"
	"sync"
	"time"
)

var sfg = &singleflight.Group{}

type GameService struct {
	sync.RWMutex
	data   []*model.GameService
	time   time.Time
	stopCh chan int
	stopWg sync.WaitGroup
}

var service = &GameService{
	stopCh: make(chan int),
}

func GetInstance() *GameService {
	return service
}

func (slf *GameService) Start() {
	slf.stopWg.Add(1)
	safe.Go(func() {
		ticker := time.NewTicker(time.Second * 3)
		defer func() {
			logx.Info("GameService:Start 工作协程退出")
			ticker.Stop()
			slf.stopWg.Done()
		}()

		for {
			select {
			case <-slf.stopCh:
				return
			case <-ticker.C:
				slf.CheckServiceList()
			}
		}
	})
}

func (slf *GameService) Stop() {
	close(slf.stopCh)
	slf.stopWg.Wait()
}

// CheckServiceList 检查游戏服务器的运行情况
func (slf *GameService) CheckServiceList() {
	list, err := slf.GetServiceList()
	if err != nil {
		return
	}

	nowTime := time.Now().Unix()
	g := &errgroup.Group{}
	for i := 0; i < len(list); i++ {
		if list[i].IsStop {
			continue
		}

		// 刚刚检查过，跳过
		if nowTime-list[i].TryTime.Load() <= 2 {
			continue
		}

		temp := i // 避免闭包变量问题
		g.Go(func() error {
			item := list[temp]
			if err := slf.CheckAddr4(context.TODO(), item); err != nil {
				item.FailTimes.Add(1)
			} else {
				item.FailTimes.Store(0)
			}
			// 再关闭一次(非必要)
			failTimes := item.FailTimes.Load()
			if failTimes >= 5 && failTimes <= 50 && failTimes%10 == 5 {
				logx.Infof("CheckServiceList failTimes:%v CloseByGameSrv", failTimes)
				usermgr.GetInstance().CloseByGameSrv(item.SrvID)
			}
			item.TryTime.Store(nowTime)
			return nil
		})
	}
	_ = g.Wait()
}

func (slf *GameService) IsSrvHealth(srvID string) bool {
	list, _ := slf.GetServiceList()
	for _, v := range list {
		if v.SrvID != srvID {
			continue
		}
		if v.FailTimes.Load() >= 3 {
			return false
		}
	}
	return true
}

// GetServiceList 获取游戏服务器列表
func (slf *GameService) GetServiceList() ([]*model.GameService, error) {
	slf.Lock()
	defer slf.Unlock()

	// 缓存过期时间，20秒
	if time.Now().Unix()-slf.time.Unix() < 20 {
		return slf.data, nil
	}

	list, err := model.NewGameServiceSearch().SetGameID(int(conf.Conf.Server.GameId)).FindNotTotal()
	if err != nil {
		return list, err
	}
	sort.Slice(list, func(i, j int) bool {
		return list[i].Sort < list[j].Sort
	})

	srvMap := make(map[string]*model.GameService)
	for _, v := range slf.data {
		srvMap[v.SrvID] = v
	}
	for _, v := range list {
		item, ok := srvMap[v.SrvID]
		if !ok {
			continue
		}
		v.FailTimes = item.FailTimes
		v.TryTime = item.TryTime
	}
	slf.data = list
	slf.time = time.Now()
	return slf.data, nil
}

// CheckAddr 检查url是否可以连通
func (slf *GameService) CheckAddr(ctx context.Context, url string) (time.Duration, error) {
	// 执行完成后会动态删除key所有可以动态添加
	v, err, _ := sfg.Do(url, func() (interface{}, error) {
		httpClient := resty.New()
		resp, err := httpClient.SetTimeout(time.Second * 5).R().SetContext(ctx).Options(url)
		if err != nil {
			logx.Infof("CheckAddr url:%v, resp:%v, err:%v", url, resp, err)
			return nil, err
		} else if !resp.IsSuccess() {
			logx.Infof("CheckAddr url:%v, resp:%v, err:%v", url, resp, err)
			return resp.Time(), errors.New(fmt.Sprintf("statusCode:%v", resp.StatusCode()))
		} else {
			logx.Infof("CheckAddr url:%v, resp:%v", url, resp)
			return resp.Time(), nil
		}
	})
	if err != nil {
		return 0, err
	} else {
		return v.(time.Duration), err
	}
}

// CheckAddr4 检查gameService是否可以连通
func (slf *GameService) CheckAddr4(ctx context.Context, gameService *model.GameService) error {
	_, err := slf.CheckAddr(ctx, httpx.UrlPathJoin(gameService.HttpDomain, gameService.HttpAddr, gameService.HttpDetectAddr))
	return err
}

// GetHealthList 获取可用的地址列表(接口畅通)
func (slf *GameService) GetHealthList() []*model.GameService {
	list, err := slf.GetServiceList()
	if err != nil {
		return []*model.GameService{}
	}

	// errgroup 是 golang.org/x/sync/errgroup 包提供的一个并发任务管理工具
	// 允许多个 Goroutine 并行执行，并且在其中一个任务失败时，能自动取消所有其他任务
	g := &errgroup.Group{}
	for i := 0; i < len(list); i++ {
		if list[i].IsStop {
			continue
		}
		temp := i // 避免闭包变量问题
		g.Go(func() error {
			item := list[temp]
			if err := slf.CheckAddr4(context.TODO(), item); err != nil {
				item.FailTimes.Add(1)
			} else {
				item.FailTimes.Store(0)
			}
			// 再关闭一次(非必要)
			failTimes := item.FailTimes.Load()
			if failTimes >= 5 && failTimes <= 50 && failTimes%10 == 5 {
				logx.Infof("GetHealthList failTimes:%v CloseByGameSrv", failTimes)
				usermgr.GetInstance().CloseByGameSrv(item.SrvID)
			}
			item.TryTime.Store(time.Now().Unix())
			return nil
		})
	}
	_ = g.Wait()

	// 只保留可用的地址
	var okList = make([]*model.GameService, 0)
	for _, v := range list {
		if v.IsStop {
			continue
		}
		if v.FailTimes.Load() > 0 {
			continue
		}
		okList = append(okList, v)
	}
	return okList
}

// GetUserGameService 玩家选择一个游戏服务器
func (slf *GameService) GetUserGameService(userID string) (*model.GameService, int) {
	// 获取可用的地址列表(接口畅通)
	healthList := slf.GetHealthList()
	if len(healthList) == 0 {
		logx.Infof("userID:%v GetHealthList no find", userID)
		return &model.GameService{}, ecode.ErrNoGameSrv
	}

	// 获取所有游戏服务器的统计列表
	statList, _ := dao.GroupDao.GameSrvStat.HGetAll()
	statMap := make(map[string]*dao.GameSrvStat)
	for _, v := range statList {
		statMap[v.SrvID] = v
	}

	// 获取可分配的服务列表
	var approveList []*model.GameService
	for _, v := range healthList {
		// 找不到统计信息，可以分配
		stat := statMap[v.SrvID]
		if stat == nil {
			approveList = append(approveList, v)
			continue
		}

		// 配置了最大人数，并且已超过，不分配
		if v.LimitCount > 0 && stat.PlayerCount >= v.LimitCount {
			continue
		}

		approveList = append(approveList, v)
	}

	var gameService *model.GameService
	if len(approveList) == 0 {
		// 如何没有可分配的，把语聊房ID转为int取余
		gameService = healthList[convertx.StringHashToInt(userID)%len(healthList)]
	} else {
		// 有可分配的服务，取第一个
		gameService = approveList[0]
	}
	return gameService, ecode.OK
}

// GetUserVoiceService 玩家选择一个语聊房服务器(可能并发，所以需要分布式加锁)
func (slf *GameService) GetUserVoiceService(appChannel string, appID int64, platRoomID string) (*model.GameService, int) {
	dao.GroupDao.GlobalLock.Lock(platRoomID, time.Second*5)
	defer dao.GroupDao.GlobalLock.Unlock(platRoomID)

	voiceInfo, err := dao.GroupDao.VoiceInfo.Get(appChannel, appID, platRoomID)
	if err != nil && !errors.Is(err, redis.Nil) {
		return &model.GameService{}, ecode.ErrRedis
	}
	if len(voiceInfo.SrvID) > 0 {
		return &model.GameService{SrvID: voiceInfo.SrvID}, ecode.OK
	}

	// 获取可用的地址列表(接口畅通)
	healthList := slf.GetHealthList()
	if len(healthList) == 0 {
		logx.Infof("platRoomID:%v GetHealthList no find", platRoomID)
		return &model.GameService{}, ecode.ErrNoGameSrv
	}

	// 获取所有游戏服务器的统计列表
	statList, _ := dao.GroupDao.GameSrvStat.HGetAll()
	statMap := make(map[string]*dao.GameSrvStat)
	for _, v := range statList {
		statMap[v.SrvID] = v
	}

	// 获取可分配的服务列表
	var approveList []*model.GameService
	for _, v := range healthList {
		// 找不到统计信息，可以分配
		stat := statMap[v.SrvID]
		if stat == nil {
			approveList = append(approveList, v)
			continue
		}

		// 配置了最大人数(不包括旁观者)，并且已超过，不分配
		if v.LimitCount > 0 && stat.PlayerCount >= v.LimitCount {
			continue
		}

		approveList = append(approveList, v)
	}

	var gameService *model.GameService
	if len(approveList) == 0 {
		// 如何没有可分配的，把语聊房ID转为int取余
		gameService = healthList[convertx.StringHashToInt(platRoomID)%len(healthList)]
	} else {
		// 有可分配的服务，取第一个
		gameService = approveList[0]
	}

	// 初始化语聊房信息
	_ = dao.GroupDao.VoiceInfo.Set(appChannel, appID, platRoomID, &dao.VoiceInfo{SrvID: gameService.SrvID, FreshTime: time.Now().Unix()})
	return gameService, ecode.OK
}

// QueryVoiceRoom 查询语聊房信息
func (slf *GameService) QueryVoiceRoom(params *request.QueryVoiceRoom) ([]interface{}, error) {
	list, _ := slf.GetServiceList()
	data := make([]interface{}, 0)
	g := &errgroup.Group{}
	for i := 0; i < len(list); i++ {
		temp := i
		g.Go(func() error {
			gameService := list[temp]
			url := httpx.UrlPathJoin(gameService.HttpDomain, gameService.HttpAddr, "query_voice_room")
			_, dataBytes, err := httpx.SendPost(url, params)
			if err != nil {
				logx.Infof("QueryVoiceRoom url:%v, err:%v", url, err)
				return nil
			}

			resp := &response.QueryVoiceStatusResp{}
			_ = json.Unmarshal(dataBytes, resp)
			if len(resp.Data) == 0 {
				return nil
			}

			data = append(data, resp.Data...)
			return nil
		})
	}
	_ = g.Wait()

	return data, nil
}

// CreateGame 快速创建语聊房游戏
func (slf *GameService) CreateGame(srvID string, params interface{}) (interface{}, int) {
	list, _ := slf.GetServiceList()
	var gameService *model.GameService
	for _, v := range list {
		if v.SrvID != srvID {
			continue
		}
		gameService = v
	}
	if gameService == nil {
		return nil, ecode.ErrNoGameSrv
	}

	url := httpx.UrlPathJoin(gameService.HttpDomain, gameService.HttpAddr, "create_game")
	statusCode, dataBytes, err := httpx.SendPost(url, params)
	logx.Infof("SendPost dataBytes:%v, err:%v, statusCode:%v", string(dataBytes), err, statusCode)
	if err != nil || statusCode != 200 {
		logx.Infof("CreateGame url:%v, err:%v, statusCode:%v", url, err, statusCode)
		return nil, ecode.ErrNoGameSrv
	}

	var data interface{}
	_ = json.Unmarshal(dataBytes, &data)
	return data, ecode.OK
}

// CloseGame 关闭语聊房游戏
func (slf *GameService) CloseGame(srvID string, params interface{}) (interface{}, int) {
	list, _ := slf.GetServiceList()
	var gameService *model.GameService
	for _, v := range list {
		if v.SrvID != srvID {
			continue
		}
		gameService = v
	}
	if gameService == nil {
		return nil, ecode.ErrNoGameSrv
	}

	url := httpx.UrlPathJoin(gameService.HttpDomain, gameService.HttpAddr, "close_game")
	statusCode, dataBytes, err := httpx.SendPost(url, params)
	if err != nil || statusCode != 200 {
		logx.Infof("CloseGame url:%v, err:%v", url, err)
		return nil, ecode.ErrNoGameSrv
	}

	var data interface{}
	_ = json.Unmarshal(dataBytes, &data)
	return data, ecode.OK
}

// UserLeave 玩家离开游戏
func (slf *GameService) UserLeave(srvID string, params interface{}) (interface{}, int) {
	list, _ := slf.GetServiceList()
	var gameService *model.GameService
	for _, v := range list {
		if v.SrvID != srvID {
			continue
		}
		gameService = v
	}
	if gameService == nil {
		return nil, ecode.ErrNoGameSrv
	}

	url := httpx.UrlPathJoin(gameService.HttpDomain, gameService.HttpAddr, "user_leave")
	statusCode, dataBytes, err := httpx.SendPost(url, params)
	if err != nil || statusCode != 200 {
		logx.Infof("UserLeave url:%v, err:%v", url, err)
		return nil, ecode.ErrNoGameSrv
	}

	var data interface{}
	_ = json.Unmarshal(dataBytes, &data)
	return data, ecode.OK
}

// AddRobot 添加机器人
func (slf *GameService) AddRobot(srvID string, params interface{}) (interface{}, int) {
	list, _ := slf.GetServiceList()
	var gameService *model.GameService
	for _, v := range list {
		if v.SrvID != srvID {
			continue
		}
		gameService = v
	}
	if gameService == nil {
		return nil, ecode.ErrNoGameSrv
	}

	url := httpx.UrlPathJoin(gameService.HttpDomain, gameService.HttpAddr, "add_robot")
	statusCode, dataBytes, err := httpx.SendPost(url, params)
	if err != nil || statusCode != 200 {
		logx.Infof("AddRobot url:%v, err:%v", url, err)
		return nil, ecode.ErrNoGameSrv
	}

	var data interface{}
	_ = json.Unmarshal(dataBytes, &data)
	return data, ecode.OK
}

// KickOutRobot 踢出机器人
func (slf *GameService) KickOutRobot(srvID string, params interface{}) (interface{}, int) {
	list, _ := slf.GetServiceList()
	var gameService *model.GameService
	for _, v := range list {
		if v.SrvID != srvID {
			continue
		}
		gameService = v
	}
	if gameService == nil {
		return nil, ecode.ErrNoGameSrv
	}

	url := httpx.UrlPathJoin(gameService.HttpDomain, gameService.HttpAddr, "kick_out_robot")
	statusCode, dataBytes, err := httpx.SendPost(url, params)
	if err != nil || statusCode != 200 {
		logx.Infof("KickOutRobot url:%v, err:%v", url, err)
		return nil, ecode.ErrNoGameSrv
	}

	var data interface{}
	_ = json.Unmarshal(dataBytes, &data)
	return data, ecode.OK
}
