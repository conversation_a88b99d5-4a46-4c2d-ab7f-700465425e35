package initialize

import (
	"minesweep/common/logx"
	"minesweep/model"
)

// InitLevelData 初始化关卡配置数据
func InitLevelData() error {
	// 检查是否已经初始化过
	search := model.NewLevelConfigSearch()
	existingLevels, _, err := search.SetIsActive(true).Find()
	if err != nil {
		logx.Errorf("检查现有关卡数据失败: %v", err)
		return err
	}

	// 如果已经有数据，跳过初始化
	if len(existingLevels) > 0 {
		logx.Infof("关卡数据已存在，跳过初始化。现有关卡数量: %d", len(existingLevels))
		return nil
	}

	// 创建30关关卡配置数据（特殊关卡使用六边形地图）
	levels := []*model.LevelConfig{
		// 1-4关：8*8方格地图
		{
			LevelID:   1,     // 关卡编号：第1关
			MapType:   0,     // 地图类型：0=方格地图，1=六边形地图
			MapWidth:  8,     // 地图宽度：8格（仅方格地图使用）
			MapHeight: 8,     // 地图高度：8格（仅方格地图使用）
			HexMapID:  0,     // 六边形地图ID：0表示不使用六边形地图
			MineCount: 8,     // 地雷数量：8颗地雷
			IsSpecial: false, // 是否特殊关卡：false=普通关卡
			IsActive:  true,  // 是否激活：true=关卡可用
		},
		{
			LevelID:   2,     // 关卡编号：第2关
			MapType:   0,     // 地图类型：方格地图
			MapWidth:  8,     // 地图宽度：8格
			MapHeight: 8,     // 地图高度：8格
			HexMapID:  0,     // 六边形地图ID：不使用
			MineCount: 9,     // 地雷数量：9颗地雷
			IsSpecial: false, // 是否特殊关卡：普通关卡
			IsActive:  true,  // 是否激活：关卡可用
		},
		{
			LevelID:   3,     // 关卡编号：第3关
			MapType:   0,     // 地图类型：方格地图
			MapWidth:  8,     // 地图宽度：8格
			MapHeight: 8,     // 地图高度：8格
			HexMapID:  0,     // 六边形地图ID：不使用
			MineCount: 10,    // 地雷数量：10颗地雷
			IsSpecial: false, // 是否特殊关卡：普通关卡
			IsActive:  true,  // 是否激活：关卡可用
		},
		{
			LevelID:   4,     // 关卡编号：第4关
			MapType:   0,     // 地图类型：方格地图
			MapWidth:  8,     // 地图宽度：8格
			MapHeight: 8,     // 地图高度：8格
			HexMapID:  0,     // 六边形地图ID：不使用
			MineCount: 11,    // 地雷数量：11颗地雷
			IsSpecial: false, // 是否特殊关卡：普通关卡
			IsActive:  true,  // 是否激活：关卡可用
		},
		// 第5关：特殊关卡，六边形地图，4颗地雷
		{
			LevelID:   5,    // 关卡编号：第5关
			MapType:   1,    // 地图类型：六边形地图
			MapWidth:  0,    // 地图宽度：0（六边形地图不使用此字段）
			MapHeight: 0,    // 地图高度：0（六边形地图不使用此字段）
			HexMapID:  0,    // 六边形地图ID：0对应第一个六边形地图配置
			MineCount: 4,    // 地雷数量：4颗地雷
			IsSpecial: true, // 是否特殊关卡：true=特殊关卡
			IsActive:  true, // 是否激活：关卡可用
		},

		// 6-9关：8*9方格地图
		{
			LevelID:   6,     // 关卡编号：第6关
			MapType:   0,     // 地图类型：方格地图
			MapWidth:  8,     // 地图宽度：8格
			MapHeight: 9,     // 地图高度：9格
			HexMapID:  0,     // 六边形地图ID：不使用
			MineCount: 9,     // 地雷数量：9颗地雷
			IsSpecial: false, // 是否特殊关卡：普通关卡
			IsActive:  true,  // 是否激活：关卡可用
		},
		{
			LevelID:   7,     // 关卡编号：第7关
			MapType:   0,     // 地图类型：方格地图
			MapWidth:  8,     // 地图宽度：8格
			MapHeight: 9,     // 地图高度：9格
			HexMapID:  0,     // 六边形地图ID：不使用
			MineCount: 10,    // 地雷数量：10颗地雷
			IsSpecial: false, // 是否特殊关卡：普通关卡
			IsActive:  true,  // 是否激活：关卡可用
		},
		{
			LevelID:   8,     // 关卡编号：第8关
			MapType:   0,     // 地图类型：方格地图
			MapWidth:  8,     // 地图宽度：8格
			MapHeight: 9,     // 地图高度：9格
			HexMapID:  0,     // 六边形地图ID：不使用
			MineCount: 11,    // 地雷数量：11颗地雷
			IsSpecial: false, // 是否特殊关卡：普通关卡
			IsActive:  true,  // 是否激活：关卡可用
		},
		{
			LevelID:   9,     // 关卡编号：第9关
			MapType:   0,     // 地图类型：方格地图
			MapWidth:  8,     // 地图宽度：8格
			MapHeight: 9,     // 地图高度：9格
			HexMapID:  0,     // 六边形地图ID：不使用
			MineCount: 12,    // 地雷数量：12颗地雷
			IsSpecial: false, // 是否特殊关卡：普通关卡
			IsActive:  true,  // 是否激活：关卡可用
		},
		// 第10关：特殊关卡，六边形地图，5颗地雷
		{
			LevelID:   10,   // 关卡编号：第10关
			MapType:   1,    // 地图类型：六边形地图
			MapWidth:  0,    // 地图宽度：0（六边形地图不使用此字段）
			MapHeight: 0,    // 地图高度：0（六边形地图不使用此字段）
			HexMapID:  1,    // 六边形地图ID：1对应第二个六边形地图配置
			MineCount: 5,    // 地雷数量：5颗地雷
			IsSpecial: true, // 是否特殊关卡：true=特殊关卡
			IsActive:  true, // 是否激活：关卡可用
		},

		// 11-14关：9*9方格地图
		{
			LevelID:   11,    // 关卡编号：第11关
			MapType:   0,     // 地图类型：方格地图
			MapWidth:  9,     // 地图宽度：9格
			MapHeight: 9,     // 地图高度：9格
			HexMapID:  0,     // 六边形地图ID：不使用
			MineCount: 10,    // 地雷数量：10颗地雷
			IsSpecial: false, // 是否特殊关卡：普通关卡
			IsActive:  true,  // 是否激活：关卡可用
		},
		{
			LevelID:   12,    // 关卡编号：第12关
			MapType:   0,     // 地图类型：方格地图
			MapWidth:  9,     // 地图宽度：9格
			MapHeight: 9,     // 地图高度：9格
			HexMapID:  0,     // 六边形地图ID：不使用
			MineCount: 11,    // 地雷数量：11颗地雷
			IsSpecial: false, // 是否特殊关卡：普通关卡
			IsActive:  true,  // 是否激活：关卡可用
		},
		{
			LevelID:   13,    // 关卡编号：第13关
			MapType:   0,     // 地图类型：方格地图
			MapWidth:  9,     // 地图宽度：9格
			MapHeight: 9,     // 地图高度：9格
			HexMapID:  0,     // 六边形地图ID：不使用
			MineCount: 12,    // 地雷数量：12颗地雷
			IsSpecial: false, // 是否特殊关卡：普通关卡
			IsActive:  true,  // 是否激活：关卡可用
		},
		{
			LevelID:   14,    // 关卡编号：第14关
			MapType:   0,     // 地图类型：方格地图
			MapWidth:  9,     // 地图宽度：9格
			MapHeight: 9,     // 地图高度：9格
			HexMapID:  0,     // 六边形地图ID：不使用
			MineCount: 13,    // 地雷数量：13颗地雷
			IsSpecial: false, // 是否特殊关卡：普通关卡
			IsActive:  true,  // 是否激活：关卡可用
		},
		// 第15关：特殊关卡，六边形地图，10颗地雷
		{
			LevelID:   15,   // 关卡编号：第15关
			MapType:   1,    // 地图类型：六边形地图
			MapWidth:  0,    // 地图宽度：0（六边形地图不使用此字段）
			MapHeight: 0,    // 地图高度：0（六边形地图不使用此字段）
			HexMapID:  2,    // 六边形地图ID：2对应第三个六边形地图配置
			MineCount: 10,   // 地雷数量：10颗地雷
			IsSpecial: true, // 是否特殊关卡：true=特殊关卡
			IsActive:  true, // 是否激活：关卡可用
		},

		// 16-19关：9*10方格地图，地雷数量递增
		{LevelID: 16, MapType: 0, MapWidth: 9, MapHeight: 10, HexMapID: 0, MineCount: 11, IsSpecial: false, IsActive: true}, // 第16关：9x10方格，11颗地雷
		{LevelID: 17, MapType: 0, MapWidth: 9, MapHeight: 10, HexMapID: 0, MineCount: 12, IsSpecial: false, IsActive: true}, // 第17关：9x10方格，12颗地雷
		{LevelID: 18, MapType: 0, MapWidth: 9, MapHeight: 10, HexMapID: 0, MineCount: 13, IsSpecial: false, IsActive: true}, // 第18关：9x10方格，13颗地雷
		{LevelID: 19, MapType: 0, MapWidth: 9, MapHeight: 10, HexMapID: 0, MineCount: 14, IsSpecial: false, IsActive: true}, // 第19关：9x10方格，14颗地雷
		// 第20关：特殊关卡，六边形地图，15颗地雷
		{
			LevelID:   20,   // 关卡编号：第20关
			MapType:   1,    // 地图类型：六边形地图
			MapWidth:  0,    // 地图宽度：0（六边形地图不使用此字段）
			MapHeight: 0,    // 地图高度：0（六边形地图不使用此字段）
			HexMapID:  3,    // 六边形地图ID：3对应第四个六边形地图配置
			MineCount: 15,   // 地雷数量：15颗地雷
			IsSpecial: true, // 是否特殊关卡：true=特殊关卡
			IsActive:  true, // 是否激活：关卡可用
		},

		// 21-24关：10*10方格地图，地雷数量递增
		{LevelID: 21, MapType: 0, MapWidth: 10, MapHeight: 10, HexMapID: 0, MineCount: 12, IsSpecial: false, IsActive: true}, // 第21关：10x10方格，12颗地雷
		{LevelID: 22, MapType: 0, MapWidth: 10, MapHeight: 10, HexMapID: 0, MineCount: 13, IsSpecial: false, IsActive: true}, // 第22关：10x10方格，13颗地雷
		{LevelID: 23, MapType: 0, MapWidth: 10, MapHeight: 10, HexMapID: 0, MineCount: 14, IsSpecial: false, IsActive: true}, // 第23关：10x10方格，14颗地雷
		{LevelID: 24, MapType: 0, MapWidth: 10, MapHeight: 10, HexMapID: 0, MineCount: 15, IsSpecial: false, IsActive: true}, // 第24关：10x10方格，15颗地雷
		// 第25关：特殊关卡，六边形地图，18颗地雷
		{
			LevelID:   25,   // 关卡编号：第25关
			MapType:   1,    // 地图类型：六边形地图
			MapWidth:  0,    // 地图宽度：0（六边形地图不使用此字段）
			MapHeight: 0,    // 地图高度：0（六边形地图不使用此字段）
			HexMapID:  4,    // 六边形地图ID：4对应第五个六边形地图配置
			MineCount: 18,   // 地雷数量：18颗地雷
			IsSpecial: true, // 是否特殊关卡：true=特殊关卡
			IsActive:  true, // 是否激活：关卡可用
		},

		// 26-29关：10*10方格地图，地雷数量递增
		{LevelID: 26, MapType: 0, MapWidth: 10, MapHeight: 10, HexMapID: 0, MineCount: 16, IsSpecial: false, IsActive: true}, // 第26关：10x10方格，16颗地雷
		{LevelID: 27, MapType: 0, MapWidth: 10, MapHeight: 10, HexMapID: 0, MineCount: 17, IsSpecial: false, IsActive: true}, // 第27关：10x10方格，17颗地雷
		{LevelID: 28, MapType: 0, MapWidth: 10, MapHeight: 10, HexMapID: 0, MineCount: 18, IsSpecial: false, IsActive: true}, // 第28关：10x10方格，18颗地雷
		{LevelID: 29, MapType: 0, MapWidth: 10, MapHeight: 10, HexMapID: 0, MineCount: 19, IsSpecial: false, IsActive: true}, // 第29关：10x10方格，19颗地雷
		// 第30关：特殊关卡，六边形地图，25颗地雷（最终关卡）
		{
			LevelID:   30,   // 关卡编号：第30关（最终关卡）
			MapType:   1,    // 地图类型：六边形地图
			MapWidth:  0,    // 地图宽度：0（六边形地图不使用此字段）
			MapHeight: 0,    // 地图高度：0（六边形地图不使用此字段）
			HexMapID:  5,    // 六边形地图ID：5对应第六个六边形地图配置
			MineCount: 25,   // 地雷数量：25颗地雷（最高难度）
			IsSpecial: true, // 是否特殊关卡：true=特殊关卡
			IsActive:  true, // 是否激活：关卡可用
		},
	}

	// 批量插入关卡配置
	err = search.CreateBatch(levels)
	if err != nil {
		logx.Errorf("初始化关卡配置失败: %v", err)
		return err
	}

	logx.Infof("成功初始化 %d 个关卡配置", len(levels))

	// 统计特殊关卡数量
	specialCount := 0
	for _, level := range levels {
		if level.IsSpecial {
			specialCount++
		}
	}

	logx.Infof("关卡系统初始化完成 - 总关卡: %d, 特殊关卡: %d", len(levels), specialCount)
	return nil
}
