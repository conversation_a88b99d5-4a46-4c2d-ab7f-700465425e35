package initialize

import (
	"minesweep/common/gormx"
	"minesweep/common/logx"
	"minesweep/model"
)

// RegisterTables 注册数据库表专用
func RegisterTables() error {
	err := gormx.GetDB().AutoMigrate(
		model.UserPackage{},
		model.UserSkin{},
		model.LevelConfig{},       // 关卡配置表
		model.UserLevelProgress{}, // 用户关卡进度表
	)
	if err != nil {
		logx.Errorf("register table err:%v", err)
		return err
	}
	return nil
}
