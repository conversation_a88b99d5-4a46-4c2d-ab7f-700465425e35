package roommgr

import (
	"fmt"
	"math/rand"
	"time"

	"minesweep/constvar"
)

// AIDecisionEngine AI决策引擎
type AIDecisionEngine struct {
	room   *Room
	random *rand.Rand
}

// AIOperation AI操作
type AIOperation struct {
	X      int // X坐标
	Y      int // Y坐标
	Q      int // 六边形Q坐标（六边形地图使用）
	R      int // 六边形R坐标（六边形地图使用）
	Action int // 操作类型（1:挖掘 2:标记）
}

// NewAIDecisionEngine 创建AI决策引擎
func NewAIDecisionEngine(room *Room) *AIDecisionEngine {
	return &AIDecisionEngine{
		room:   room,
		random: rand.New(rand.NewSource(time.Now().UnixNano())),
	}
}

// MakeDecision 做出AI决策
func (engine *AIDecisionEngine) MakeDecision(userID string) (*AIOperation, error) {
	if engine.room.mineMap == nil {
		return nil, fmt.Errorf("地图未初始化")
	}

	switch engine.room.MapType {
	case constvar.MapTypeGrid:
		return engine.makeGridDecision(userID)
	case constvar.MapTypeHexagon:
		return engine.makeHexDecision(userID)
	default:
		return nil, fmt.Errorf("不支持的地图类型: %d", engine.room.MapType)
	}
}

// makeGridDecision 方格地图决策
func (engine *AIDecisionEngine) makeGridDecision(userID string) (*AIOperation, error) {
	// 获取所有可操作的方块
	availableBlocks := engine.getAvailableGridBlocks()
	if len(availableBlocks) == 0 {
		return nil, fmt.Errorf("没有可操作的方块")
	}

	// 尝试智能决策
	if operation := engine.tryIntelligentGridDecision(availableBlocks); operation != nil {
		return operation, nil
	}

	// 回退到随机决策
	return engine.makeRandomGridDecision(availableBlocks), nil
}

// makeHexDecision 六边形地图决策
func (engine *AIDecisionEngine) makeHexDecision(userID string) (*AIOperation, error) {
	// 获取所有可操作的六边形方块
	availableHexes := engine.getAvailableHexBlocks()
	if len(availableHexes) == 0 {
		return nil, fmt.Errorf("没有可操作的六边形方块")
	}

	// 尝试智能决策
	if operation := engine.tryIntelligentHexDecision(availableHexes); operation != nil {
		return operation, nil
	}

	// 回退到随机决策
	return engine.makeRandomHexDecision(availableHexes), nil
}

// getAvailableGridBlocks 获取可操作的方格方块
func (engine *AIDecisionEngine) getAvailableGridBlocks() []struct{ X, Y int } {
	var available []struct{ X, Y int }

	for y := 0; y < engine.room.mineMap.Height; y++ {
		for x := 0; x < engine.room.mineMap.Width; x++ {
			block := engine.room.mineMap.GetBlock(x, y)
			if block != nil && !block.IsRevealed && !block.IsMarked {
				available = append(available, struct{ X, Y int }{X: x, Y: y})
			}
		}
	}

	return available
}

// getAvailableHexBlocks 获取可操作的六边形方块
func (engine *AIDecisionEngine) getAvailableHexBlocks() []struct{ Q, R int } {
	var available []struct{ Q, R int }

	for _, hex := range engine.room.mineMap.ValidHexes {
		coordKey := fmt.Sprintf("%d,%d", hex.Q, hex.R)
		block := engine.room.mineMap.HexBlocks[coordKey]
		if block != nil && !block.IsRevealed && !block.IsMarked {
			available = append(available, struct{ Q, R int }{Q: hex.Q, R: hex.R})
		}
	}

	return available
}

// tryIntelligentGridDecision 尝试智能方格决策
func (engine *AIDecisionEngine) tryIntelligentGridDecision(availableBlocks []struct{ X, Y int }) *AIOperation {
	// 策略1: 寻找明显安全的方块（周围已挖掘方块的数字等于周围已标记地雷数）
	for _, pos := range availableBlocks {
		if engine.isSafeGridBlock(pos.X, pos.Y) {
			return &AIOperation{
				X:      pos.X,
				Y:      pos.Y,
				Action: 1, // 挖掘
			}
		}
	}

	// 策略2: 寻找明显的地雷位置（周围已挖掘方块的数字等于周围未操作方块数）
	for _, pos := range availableBlocks {
		if engine.isObviousMineGridBlock(pos.X, pos.Y) {
			return &AIOperation{
				X:      pos.X,
				Y:      pos.Y,
				Action: 2, // 标记
			}
		}
	}

	// 策略3: 寻找相对安全的方块（基于安全评分）
	bestSafetyScore := -1
	var bestSafePos *struct{ X, Y int }

	for _, pos := range availableBlocks {
		safetyScore := engine.calculateGridSafetyScore(pos.X, pos.Y)
		if safetyScore > bestSafetyScore {
			bestSafetyScore = safetyScore
			bestSafePos = &pos
		}
	}

	if bestSafePos != nil && bestSafetyScore > 0 {
		return &AIOperation{
			X:      bestSafePos.X,
			Y:      bestSafePos.Y,
			Action: 1, // 挖掘相对安全的方块
		}
	}

	// 策略4: 寻找角落和边缘位置（通常较安全）
	for _, pos := range availableBlocks {
		if engine.isGridCornerOrEdge(pos.X, pos.Y) {
			return &AIOperation{
				X:      pos.X,
				Y:      pos.Y,
				Action: 1, // 挖掘
			}
		}
	}

	return nil // 没有明显的智能选择
}

// tryIntelligentHexDecision 尝试智能六边形决策
func (engine *AIDecisionEngine) tryIntelligentHexDecision(availableHexes []struct{ Q, R int }) *AIOperation {
	// 策略1: 寻找明显安全的六边形方块
	for _, pos := range availableHexes {
		if engine.isSafeHexBlock(pos.Q, pos.R) {
			return &AIOperation{
				Q:      pos.Q,
				R:      pos.R,
				Action: 1, // 挖掘
			}
		}
	}

	// 策略2: 寻找明显的地雷位置
	for _, pos := range availableHexes {
		if engine.isObviousMineHexBlock(pos.Q, pos.R) {
			return &AIOperation{
				Q:      pos.Q,
				R:      pos.R,
				Action: 2, // 标记
			}
		}
	}

	// 策略3: 寻找相对安全的方块（周围已挖掘方块较多）
	bestSafetyScore := -1
	var bestSafePos *struct{ Q, R int }

	for _, pos := range availableHexes {
		safetyScore := engine.calculateHexSafetyScore(pos.Q, pos.R)
		if safetyScore > bestSafetyScore {
			bestSafetyScore = safetyScore
			bestSafePos = &pos
		}
	}

	if bestSafePos != nil && bestSafetyScore > 0 {
		return &AIOperation{
			Q:      bestSafePos.Q,
			R:      bestSafePos.R,
			Action: 1, // 挖掘相对安全的方块
		}
	}

	// 策略4: 模式识别 - 寻找常见的安全模式
	if operation := engine.findHexSafePatterns(availableHexes); operation != nil {
		return operation
	}

	// 策略5: 概率分析 - 选择地雷概率最低的方块
	if operation := engine.findLowestProbabilityHex(availableHexes); operation != nil {
		return operation
	}

	return nil // 没有明显的智能选择
}

// makeRandomGridDecision 随机方格决策
func (engine *AIDecisionEngine) makeRandomGridDecision(availableBlocks []struct{ X, Y int }) *AIOperation {
	// 随机选择一个方块
	pos := availableBlocks[engine.random.Intn(len(availableBlocks))]

	// 随机选择操作类型（70%挖掘，30%标记）
	action := 1 // 挖掘
	if engine.random.Float32() < 0.3 {
		action = 2 // 标记
	}

	return &AIOperation{
		X:      pos.X,
		Y:      pos.Y,
		Action: action,
	}
}

// makeRandomHexDecision 随机六边形决策
func (engine *AIDecisionEngine) makeRandomHexDecision(availableHexes []struct{ Q, R int }) *AIOperation {
	// 随机选择一个六边形方块
	pos := availableHexes[engine.random.Intn(len(availableHexes))]

	// 随机选择操作类型（70%挖掘，30%标记）
	action := 1 // 挖掘
	if engine.random.Float32() < 0.3 {
		action = 2 // 标记
	}

	return &AIOperation{
		Q:      pos.Q,
		R:      pos.R,
		Action: action,
	}
}

// isSafeGridBlock 检查方格方块是否明显安全
func (engine *AIDecisionEngine) isSafeGridBlock(x, y int) bool {
	// 检查周围的已挖掘方块
	for dy := -1; dy <= 1; dy++ {
		for dx := -1; dx <= 1; dx++ {
			if dx == 0 && dy == 0 {
				continue
			}

			nx, ny := x+dx, y+dy
			if nx < 0 || nx >= engine.room.mineMap.Width || ny < 0 || ny >= engine.room.mineMap.Height {
				continue
			}

			neighborBlock := engine.room.mineMap.GetBlock(nx, ny)
			if neighborBlock != nil && neighborBlock.IsRevealed {
				// 检查这个已挖掘方块周围的地雷数是否等于已标记的地雷数
				if engine.countMarkedMinesAroundGrid(nx, ny) == neighborBlock.NeighborMines {
					return true // 这个方块是安全的
				}
			}
		}
	}

	return false
}

// isObviousMineGridBlock 检查方格方块是否明显是地雷
func (engine *AIDecisionEngine) isObviousMineGridBlock(x, y int) bool {
	// 检查周围的已挖掘方块
	for dy := -1; dy <= 1; dy++ {
		for dx := -1; dx <= 1; dx++ {
			if dx == 0 && dy == 0 {
				continue
			}

			nx, ny := x+dx, y+dy
			if nx < 0 || nx >= engine.room.mineMap.Width || ny < 0 || ny >= engine.room.mineMap.Height {
				continue
			}

			neighborBlock := engine.room.mineMap.GetBlock(nx, ny)
			if neighborBlock != nil && neighborBlock.IsRevealed {
				// 检查这个已挖掘方块周围的未操作方块数是否等于剩余地雷数
				unmarkedCount := engine.countUnmarkedBlocksAroundGrid(nx, ny)
				markedCount := engine.countMarkedMinesAroundGrid(nx, ny)
				if markedCount+unmarkedCount == neighborBlock.NeighborMines && unmarkedCount == 1 {
					return true // 这个方块明显是地雷
				}
			}
		}
	}

	return false
}

// isSafeHexBlock 检查六边形方块是否明显安全
func (engine *AIDecisionEngine) isSafeHexBlock(q, r int) bool {
	coordKey := fmt.Sprintf("%d,%d", q, r)

	// 检查周围的已挖掘六边形方块
	neighbors := engine.room.mineMap.NeighborMap[coordKey]
	if neighbors == nil {
		return false
	}

	for _, neighborKey := range neighbors {
		neighborBlock := engine.room.mineMap.HexBlocks[neighborKey]
		if neighborBlock != nil && neighborBlock.IsRevealed {
			// 检查这个已挖掘方块周围的地雷数是否等于已标记的地雷数
			if engine.countMarkedMinesAroundHex(neighborKey) == neighborBlock.NeighborMines {
				return true // 这个六边形方块是安全的
			}
		}
	}

	return false
}

// isObviousMineHexBlock 检查六边形方块是否明显是地雷
func (engine *AIDecisionEngine) isObviousMineHexBlock(q, r int) bool {
	coordKey := fmt.Sprintf("%d,%d", q, r)

	// 检查周围的已挖掘六边形方块
	neighbors := engine.room.mineMap.NeighborMap[coordKey]
	if neighbors == nil {
		return false
	}

	for _, neighborKey := range neighbors {
		neighborBlock := engine.room.mineMap.HexBlocks[neighborKey]
		if neighborBlock != nil && neighborBlock.IsRevealed {
			// 检查这个已挖掘方块周围的未操作方块数是否等于剩余地雷数
			unmarkedCount := engine.countUnmarkedBlocksAroundHex(neighborKey)
			markedCount := engine.countMarkedMinesAroundHex(neighborKey)
			if markedCount+unmarkedCount == neighborBlock.NeighborMines && unmarkedCount == 1 {
				return true // 这个六边形方块明显是地雷
			}
		}
	}

	return false
}

// countMarkedMinesAroundGrid 计算方格方块周围已标记的地雷数
func (engine *AIDecisionEngine) countMarkedMinesAroundGrid(x, y int) int {
	count := 0
	for dy := -1; dy <= 1; dy++ {
		for dx := -1; dx <= 1; dx++ {
			if dx == 0 && dy == 0 {
				continue
			}

			nx, ny := x+dx, y+dy
			if nx < 0 || nx >= engine.room.mineMap.Width || ny < 0 || ny >= engine.room.mineMap.Height {
				continue
			}

			block := engine.room.mineMap.GetBlock(nx, ny)
			if block != nil && block.IsMarked {
				count++
			}
		}
	}
	return count
}

// countUnmarkedBlocksAroundGrid 计算方格方块周围未操作的方块数
func (engine *AIDecisionEngine) countUnmarkedBlocksAroundGrid(x, y int) int {
	count := 0
	for dy := -1; dy <= 1; dy++ {
		for dx := -1; dx <= 1; dx++ {
			if dx == 0 && dy == 0 {
				continue
			}

			nx, ny := x+dx, y+dy
			if nx < 0 || nx >= engine.room.mineMap.Width || ny < 0 || ny >= engine.room.mineMap.Height {
				continue
			}

			block := engine.room.mineMap.GetBlock(nx, ny)
			if block != nil && !block.IsRevealed && !block.IsMarked {
				count++
			}
		}
	}
	return count
}

// countMarkedMinesAroundHex 计算六边形方块周围已标记的地雷数
func (engine *AIDecisionEngine) countMarkedMinesAroundHex(coordKey string) int {
	count := 0
	neighbors := engine.room.mineMap.NeighborMap[coordKey]

	for _, neighborKey := range neighbors {
		block := engine.room.mineMap.HexBlocks[neighborKey]
		if block != nil && block.IsMarked {
			count++
		}
	}

	return count
}

// countUnmarkedBlocksAroundHex 计算六边形方块周围未操作的方块数
func (engine *AIDecisionEngine) countUnmarkedBlocksAroundHex(coordKey string) int {
	count := 0
	neighbors := engine.room.mineMap.NeighborMap[coordKey]

	for _, neighborKey := range neighbors {
		block := engine.room.mineMap.HexBlocks[neighborKey]
		if block != nil && !block.IsRevealed && !block.IsMarked {
			count++
		}
	}

	return count
}

// calculateHexSafetyScore 计算六边形方块的安全评分
func (engine *AIDecisionEngine) calculateHexSafetyScore(q, r int) int {
	coordKey := fmt.Sprintf("%d,%d", q, r)
	neighbors := engine.room.mineMap.NeighborMap[coordKey]
	if neighbors == nil {
		return 0
	}

	score := 0

	// 评分因子1: 周围已挖掘方块数量（已挖掘越多越安全）
	revealedNeighbors := 0
	for _, neighborKey := range neighbors {
		block := engine.room.mineMap.HexBlocks[neighborKey]
		if block != nil && block.IsRevealed {
			revealedNeighbors++

			// 评分因子2: 已挖掘方块的地雷数越少越安全
			if block.NeighborMines == 0 {
				score += 3 // 空白格周围很安全
			} else if block.NeighborMines <= 2 {
				score += 2 // 低数字相对安全
			} else {
				score += 1 // 高数字相对危险
			}
		}
	}

	// 评分因子3: 周围已挖掘方块比例
	if len(neighbors) > 0 {
		revealedRatio := float64(revealedNeighbors) / float64(len(neighbors))
		score += int(revealedRatio * 5) // 最多加5分
	}

	// 评分因子4: 距离中心的距离（中心区域相对安全）
	centerDistance := engine.calculateHexDistanceFromCenter(q, r)
	if centerDistance <= 1 {
		score += 2 // 中心区域加分
	} else if centerDistance <= 2 {
		score += 1 // 次中心区域小幅加分
	}

	return score
}

// calculateHexDistanceFromCenter 计算六边形方块到地图中心的距离
func (engine *AIDecisionEngine) calculateHexDistanceFromCenter(q, r int) int {
	// 六边形地图的中心通常是(0,0)，计算曼哈顿距离
	centerQ, centerR := 0, 0

	// 使用六边形坐标系的距离公式
	return (abs(q-centerQ) + abs(q+r-centerQ-centerR) + abs(r-centerR)) / 2
}

// findHexSafePatterns 寻找六边形安全模式
func (engine *AIDecisionEngine) findHexSafePatterns(availableHexes []struct{ Q, R int }) *AIOperation {
	// 模式1: 寻找被多个低数字包围的方块
	for _, pos := range availableHexes {
		if engine.isSurroundedByLowNumbers(pos.Q, pos.R) {
			return &AIOperation{
				Q:      pos.Q,
				R:      pos.R,
				Action: 1, // 挖掘
			}
		}
	}

	// 模式2: 寻找角落位置（通常较安全）
	for _, pos := range availableHexes {
		if engine.isHexCornerPosition(pos.Q, pos.R) {
			return &AIOperation{
				Q:      pos.Q,
				R:      pos.R,
				Action: 1, // 挖掘
			}
		}
	}

	return nil
}

// findLowestProbabilityHex 寻找地雷概率最低的六边形方块
func (engine *AIDecisionEngine) findLowestProbabilityHex(availableHexes []struct{ Q, R int }) *AIOperation {
	if len(availableHexes) == 0 {
		return nil
	}

	lowestProbability := 1.0
	var bestPos *struct{ Q, R int }

	for _, pos := range availableHexes {
		probability := engine.calculateHexMineProbability(pos.Q, pos.R)
		if probability < lowestProbability {
			lowestProbability = probability
			bestPos = &pos
		}
	}

	if bestPos != nil && lowestProbability < 0.5 { // 只选择概率小于50%的方块
		return &AIOperation{
			Q:      bestPos.Q,
			R:      bestPos.R,
			Action: 1, // 挖掘
		}
	}

	return nil
}

// isSurroundedByLowNumbers 检查六边形方块是否被低数字包围
func (engine *AIDecisionEngine) isSurroundedByLowNumbers(q, r int) bool {
	coordKey := fmt.Sprintf("%d,%d", q, r)
	neighbors := engine.room.mineMap.NeighborMap[coordKey]
	if neighbors == nil {
		return false
	}

	revealedLowNumbers := 0
	totalRevealed := 0

	for _, neighborKey := range neighbors {
		block := engine.room.mineMap.HexBlocks[neighborKey]
		if block != nil && block.IsRevealed {
			totalRevealed++
			if block.NeighborMines <= 2 { // 认为0,1,2是低数字
				revealedLowNumbers++
			}
		}
	}

	// 如果至少有2个已挖掘邻居，且其中80%以上是低数字
	return totalRevealed >= 2 && float64(revealedLowNumbers)/float64(totalRevealed) >= 0.8
}

// isHexCornerPosition 检查是否为六边形地图的角落位置
func (engine *AIDecisionEngine) isHexCornerPosition(q, r int) bool {
	coordKey := fmt.Sprintf("%d,%d", q, r)
	neighbors := engine.room.mineMap.NeighborMap[coordKey]

	// 角落位置的特征：邻居数量少于6个（边界位置）
	return len(neighbors) <= 3
}

// calculateHexMineProbability 计算六边形方块是地雷的概率
func (engine *AIDecisionEngine) calculateHexMineProbability(q, r int) float64 {
	coordKey := fmt.Sprintf("%d,%d", q, r)
	neighbors := engine.room.mineMap.NeighborMap[coordKey]
	if neighbors == nil {
		return 0.5 // 默认概率
	}

	// 基于周围已挖掘方块的信息计算概率
	totalConstraints := 0
	totalProbability := 0.0

	for _, neighborKey := range neighbors {
		block := engine.room.mineMap.HexBlocks[neighborKey]
		if block != nil && block.IsRevealed {
			// 计算这个已挖掘方块周围的约束
			neighborNeighbors := engine.room.mineMap.NeighborMap[neighborKey]
			unmarkedCount := 0
			markedCount := 0

			for _, nnKey := range neighborNeighbors {
				nnBlock := engine.room.mineMap.HexBlocks[nnKey]
				if nnBlock != nil {
					if nnBlock.IsMarked {
						markedCount++
					} else if !nnBlock.IsRevealed {
						unmarkedCount++
					}
				}
			}

			if unmarkedCount > 0 {
				// 剩余地雷数 / 剩余未标记方块数
				remainingMines := block.NeighborMines - markedCount
				if remainingMines > 0 {
					probability := float64(remainingMines) / float64(unmarkedCount)
					totalProbability += probability
					totalConstraints++
				}
			}
		}
	}

	if totalConstraints > 0 {
		return totalProbability / float64(totalConstraints)
	}

	// 如果没有约束信息，使用全局概率
	totalBlocks := len(engine.room.mineMap.HexBlocks)
	revealedBlocks := 0
	markedBlocks := 0

	for _, block := range engine.room.mineMap.HexBlocks {
		if block.IsRevealed {
			revealedBlocks++
		} else if block.IsMarked {
			markedBlocks++
		}
	}

	remainingBlocks := totalBlocks - revealedBlocks - markedBlocks
	remainingMines := engine.room.mineMap.MineCount - markedBlocks

	if remainingBlocks > 0 {
		return float64(remainingMines) / float64(remainingBlocks)
	}

	return 0.0
}

// calculateGridSafetyScore 计算方格方块的安全评分
func (engine *AIDecisionEngine) calculateGridSafetyScore(x, y int) int {
	score := 0

	// 评分因子1: 周围已挖掘方块数量（已挖掘越多越安全）
	revealedNeighbors := 0
	for dy := -1; dy <= 1; dy++ {
		for dx := -1; dx <= 1; dx++ {
			if dx == 0 && dy == 0 {
				continue
			}

			nx, ny := x+dx, y+dy
			if nx < 0 || nx >= engine.room.mineMap.Width || ny < 0 || ny >= engine.room.mineMap.Height {
				continue
			}

			block := engine.room.mineMap.GetBlock(nx, ny)
			if block != nil && block.IsRevealed {
				revealedNeighbors++

				// 评分因子2: 已挖掘方块的地雷数越少越安全
				if block.NeighborMines == 0 {
					score += 3 // 空白格周围很安全
				} else if block.NeighborMines <= 2 {
					score += 2 // 低数字相对安全
				} else {
					score += 1 // 高数字相对危险
				}
			}
		}
	}

	// 评分因子3: 周围已挖掘方块比例
	totalNeighbors := 0
	for dy := -1; dy <= 1; dy++ {
		for dx := -1; dx <= 1; dx++ {
			if dx == 0 && dy == 0 {
				continue
			}

			nx, ny := x+dx, y+dy
			if nx >= 0 && nx < engine.room.mineMap.Width && ny >= 0 && ny < engine.room.mineMap.Height {
				totalNeighbors++
			}
		}
	}

	if totalNeighbors > 0 {
		revealedRatio := float64(revealedNeighbors) / float64(totalNeighbors)
		score += int(revealedRatio * 5) // 最多加5分
	}

	// 评分因子4: 距离中心的距离（中心区域相对安全）
	centerX, centerY := engine.room.mineMap.Width/2, engine.room.mineMap.Height/2
	distanceFromCenter := abs(x-centerX) + abs(y-centerY)
	if distanceFromCenter <= 2 {
		score += 2 // 中心区域加分
	} else if distanceFromCenter <= 4 {
		score += 1 // 次中心区域小幅加分
	}

	return score
}

// isGridCornerOrEdge 检查是否为方格地图的角落或边缘位置
func (engine *AIDecisionEngine) isGridCornerOrEdge(x, y int) bool {
	width := engine.room.mineMap.Width
	height := engine.room.mineMap.Height

	// 角落位置
	if (x == 0 || x == width-1) && (y == 0 || y == height-1) {
		return true
	}

	// 边缘位置（但不是角落）
	if x == 0 || x == width-1 || y == 0 || y == height-1 {
		// 计算邻居数量，边缘位置邻居较少
		neighborCount := 0
		for dy := -1; dy <= 1; dy++ {
			for dx := -1; dx <= 1; dx++ {
				if dx == 0 && dy == 0 {
					continue
				}

				nx, ny := x+dx, y+dy
				if nx >= 0 && nx < width && ny >= 0 && ny < height {
					neighborCount++
				}
			}
		}

		// 邻居数量少于6个的边缘位置
		return neighborCount < 6
	}

	return false
}
