package roommgr

import (
	"fmt"
	"math/rand"
	"sync"
	"time"

	"minesweep/common/logx"
	"minesweep/constvar"
)

// AIManager AI托管管理器（使用随机决策，不使用智能决策）
type AIManager struct {
	room         *Room
	managedUsers map[string]*AIUserState // 托管用户状态
	mutex        sync.RWMutex
}

// AIUserState AI托管用户状态
type AIUserState struct {
	UserID            string    // 用户ID
	StartTime         time.Time // 托管开始时间
	OperationCount    int       // 代操作次数
	LastOperationTime time.Time // 最后操作时间
	IsActive          bool      // 是否激活状态
}

// NewAIManager 创建AI托管管理器
func NewAIManager(room *Room) *AIManager {
	return &AIManager{
		room:         room,
		managedUsers: make(map[string]*AIUserState),
	}
}

// StartAIManagement 开始AI托管
func (am *AIManager) StartAIManagement(userID string) error {
	am.mutex.Lock()
	defer am.mutex.Unlock()

	// 检查用户是否已经在托管中
	if _, exists := am.managedUsers[userID]; exists {
		return fmt.Errorf("用户 %s 已经在AI托管中", userID)
	}

	// 检查是否为联机模式（关卡模式不支持AI托管）
	if am.room.IsLevelMode {
		return fmt.Errorf("关卡模式不支持AI托管")
	}

	// 创建AI托管状态
	aiState := &AIUserState{
		UserID:    userID,
		StartTime: time.Now(),
		IsActive:  true,
	}

	am.managedUsers[userID] = aiState

	// 更新房间用户状态
	am.updateRoomUserAIStatus(userID, true)

	// 广播AI托管状态变更
	am.broadcastAIStatusChange(userID, true)

	// 第一次进入AI托管时立即执行一次挖掘操作
	if am.room.gameStatus == constvar.GameStatusMinesweeping {
		err := am.performAIDigOperationInternal(userID)
		if err != nil {
			logx.Errorf("第一次进入AI托管立即操作失败 UserID:%s Error:%v", userID, err)
		} else {
			logx.Infof("第一次进入AI托管立即操作成功 UserID:%s", userID)
		}
	}
	logx.Infof("用户 %s 开始AI托管 RoomID:%d", userID, am.room.RoomID)
	return nil
}

// StopAIManagement 停止AI托管
func (am *AIManager) StopAIManagement(userID string) error {
	am.mutex.Lock()
	defer am.mutex.Unlock()

	return am.stopAIManagementInternal(userID)
}

// stopAIManagementInternal 内部停止AI托管方法（不获取锁，假设调用者已持有锁）
func (am *AIManager) stopAIManagementInternal(userID string) error {
	// 检查用户是否在托管中
	aiState, exists := am.managedUsers[userID]
	if !exists {
		return fmt.Errorf("用户 %s 不在AI托管中", userID)
	}

	// 删除托管状态
	delete(am.managedUsers, userID)

	// 更新房间用户状态
	am.updateRoomUserAIStatus(userID, false)

	// 广播AI托管状态变更
	am.broadcastAIStatusChange(userID, false)

	duration := time.Since(aiState.StartTime)
	logx.Infof("用户 %s 停止AI托管 RoomID:%d 托管时长:%v 代操作次数:%d",
		userID, am.room.RoomID, duration, aiState.OperationCount)
	return nil
}

// IsUserAIManaged 检查用户是否在AI托管中
func (am *AIManager) IsUserAIManaged(userID string) bool {
	am.mutex.RLock()
	defer am.mutex.RUnlock()

	aiState, exists := am.managedUsers[userID]
	return exists && aiState.IsActive
}

// PerformAIOperation 执行AI托管随机操作
func (am *AIManager) PerformAIOperation(userID string) error {
	am.mutex.Lock()
	defer am.mutex.Unlock()

	// 检查用户是否在托管中
	aiState, exists := am.managedUsers[userID]
	if !exists || !aiState.IsActive {
		return fmt.Errorf("用户 %s 不在AI托管中", userID)
	}

	// 检查游戏状态是否允许操作
	if am.room.gameStatus != constvar.GameStatusMinesweeping {
		return fmt.Errorf("当前游戏状态不允许操作")
	}

	// AI托管使用随机决策，不使用智能决策
	operation, err := am.room.decisionUtils.MakeRandomDecision()
	if err != nil {
		logx.Errorf("AI托管随机决策失败 UserID:%s Error:%v", userID, err)
		return err
	}

	// 执行AI操作
	err = am.executeAIOperation(userID, operation)
	if err != nil {
		logx.Errorf("AI操作执行失败 UserID:%s Error:%v", userID, err)
		return err
	}

	// 更新操作统计
	aiState.OperationCount++
	aiState.LastOperationTime = time.Now()

	logx.Infof("AI托管随机操作成功 UserID:%s X:%d Y:%d Q:%d R:%d Action:%d",
		userID, operation.X, operation.Y, operation.Q, operation.R, operation.Action)
	return nil
}

// PerformAIDigOperation 执行AI托管挖掘操作（仅限挖掘，不允许标记）
func (am *AIManager) PerformAIDigOperation(userID string) error {
	logx.Infof("开始执行AI托管挖掘操作 UserID:%s", userID)
	am.mutex.Lock()
	defer am.mutex.Unlock()

	return am.performAIDigOperationInternal(userID)
}

// performAIDigOperationInternal 内部AI托管挖掘操作（不获取锁，假设调用者已持有锁）
func (am *AIManager) performAIDigOperationInternal(userID string) error {
	// 检查用户是否在托管中
	aiState, exists := am.managedUsers[userID]
	if !exists || !aiState.IsActive {
		return fmt.Errorf("用户 %s 不在AI托管中", userID)
	}

	// 检查游戏状态是否允许操作
	if am.room.gameStatus != constvar.GameStatusMinesweeping {
		return fmt.Errorf("当前游戏状态不允许操作")
	}

	// AI托管使用随机决策，但仅限挖掘操作
	operation, err := am.room.decisionUtils.MakeRandomDigDecision()
	if err != nil {
		logx.Errorf("AI托管随机挖掘决策失败 UserID:%s Error:%v", userID, err)
		return err
	}

	// 执行AI操作
	err = am.executeAIOperation(userID, operation)
	if err != nil {
		logx.Errorf("AI挖掘操作执行失败 UserID:%s Error:%v", userID, err)
		return err
	}

	// 更新操作统计
	aiState.OperationCount++
	aiState.LastOperationTime = time.Now()

	logx.Infof("AI托管挖掘操作成功 UserID:%s X:%d Y:%d Q:%d R:%d Action:%d",
		userID, operation.X, operation.Y, operation.Q, operation.R, operation.Action)
	return nil
}

// CheckAndTriggerAI 检查并触发AI托管（现在主要用于维护已托管用户状态）
func (am *AIManager) CheckAndTriggerAI() {
	if am.room.IsLevelMode {
		return // 关卡模式不支持AI托管
	}

	// 检查当前是否为操作阶段
	if am.room.gameStatus != constvar.GameStatusMinesweeping {
		return
	}

	// 注意：AI托管现在主要通过第一次超时触发，这里主要用于维护状态
	// 确保已托管的用户在新回合开始时保持托管状态
	am.mutex.RLock()
	managedUsers := make([]string, 0, len(am.managedUsers))
	for userID := range am.managedUsers {
		managedUsers = append(managedUsers, userID)
	}
	am.mutex.RUnlock()

	// 为已托管的用户确保状态正确
	for _, userID := range managedUsers {
		if !am.hasUserOperatedThisRound(userID) {
			// 已托管用户在新回合中还未操作，保持托管状态
			logx.Debugf("维护AI托管状态 UserID:%s RoomID:%d", userID, am.room.RoomID)
		}
	}
}

// PerformScheduledAIOperations 执行计划的AI托管随机操作
func (am *AIManager) PerformScheduledAIOperations() {
	am.mutex.RLock()
	managedUsers := make([]string, 0, len(am.managedUsers))
	for userID := range am.managedUsers {
		managedUsers = append(managedUsers, userID)
	}
	am.mutex.RUnlock()

	// 为每个托管用户执行操作
	for _, userID := range managedUsers {
		// 检查用户是否已经操作过
		if _, hasAction := am.room.roundActions[userID]; hasAction {
			continue // 已经操作过，跳过
		}

		// 为每个用户随机选择一个执行时间点（开局后3-5秒，对应倒计时22-20秒）
		targetTime := 20 + rand.Intn(3) // 随机选择20、21、22中的一个

		// 只在用户的指定时间点执行操作
		if am.room.countDown == targetTime {
			err := am.PerformAIDigOperation(userID)
			if err != nil {
				logx.Errorf("执行计划AI操作失败 UserID:%s Error:%v", userID, err)
			} else {
				logx.Infof("执行计划AI操作成功 UserID:%s CountDown:%d", userID, am.room.countDown)
			}
		}
	}
}

// updateRoomUserAIStatus 更新房间用户的AI状态
func (am *AIManager) updateRoomUserAIStatus(userID string, isAIManaged bool) {
	for _, user := range am.room.allUser {
		if user != nil && user.UserID == userID {
			user.IsAIManaged = isAIManaged
			if isAIManaged {
				user.AIStartTime = time.Now().Unix()
				user.AIOperationCount = 0
			} else {
				user.AIStartTime = 0
				user.AIOperationCount = 0
			}
			break
		}
	}
}

// broadcastAIStatusChange 广播AI状态变更
func (am *AIManager) broadcastAIStatusChange(userID string, isAIManaged bool) {
	// 构建AI状态变更通知
	notification := map[string]interface{}{
		"userId":      userID,
		"isAIManaged": isAIManaged,
		"timestamp":   time.Now().Unix(),
	}

	// 广播给房间内所有用户
	am.room.Broadcast(constvar.MsgTypeAIStatusChange, 0, notification)
}

// maintainAIManagedUsersForNewRound 为新回合维护已托管用户的状态
func (am *AIManager) maintainAIManagedUsersForNewRound() {
	am.mutex.RLock()
	managedUsers := make([]string, 0, len(am.managedUsers))
	for userID := range am.managedUsers {
		managedUsers = append(managedUsers, userID)
	}
	am.mutex.RUnlock()

	// 为已托管的用户确保在新回合中保持托管状态
	for _, userID := range managedUsers {
		logx.Infof("新回合维护AI托管状态 UserID:%s RoomID:%d Round:%d",
			userID, am.room.RoomID, am.room.currentRound)

		// 确保房间用户状态正确
		am.updateRoomUserAIStatus(userID, true)
	}

	if len(managedUsers) > 0 {
		logx.Infof("新回合AI托管状态维护完成 RoomID:%d Round:%d ManagedUsers:%d",
			am.room.RoomID, am.room.currentRound, len(managedUsers))
	}
}

// hasUserOperatedThisRound 检查用户在当前回合是否有操作
func (am *AIManager) hasUserOperatedThisRound(userID string) bool {
	// 检查用户是否在当前回合有操作记录
	_, hasOperated := am.room.roundActions[userID]
	return hasOperated
}

// executeAIOperation 执行AI操作
func (am *AIManager) executeAIOperation(userID string, operation *AIOperation) error {
	// 检查用户是否仍然在房间中（而不是检查全局用户管理器）
	roomUser := am.room.GetUser(userID)
	if roomUser == nil {
		logx.Errorf("AI托管用户不在房间中，停止托管 UserID:%s RoomID:%d", userID, am.room.RoomID)
		// 用户不在房间中，停止AI托管
		go func() {
			am.StopAIManagement(userID)
		}()
		return fmt.Errorf("用户 %s 不在房间中", userID)
	}

	// 检查用户是否已离开房间（离线用户仍可继续AI托管）
	if roomUser.IsLeave {
		logx.Errorf("AI托管用户已离开房间，停止托管 UserID:%s RoomID:%d", userID, am.room.RoomID)
		go func() {
			am.StopAIManagement(userID)
		}()
		return fmt.Errorf("用户 %s 已离开房间", userID)
	}

	// 直接调用房间的AI托管操作处理方法，绕过用户管理器检查
	return am.room.executeAIOperationDirect(userID, operation)
}

// GetAIStatus 获取AI托管状态
func (am *AIManager) GetAIStatus() map[string]*AIUserState {
	am.mutex.RLock()
	defer am.mutex.RUnlock()

	result := make(map[string]*AIUserState)
	for userID, state := range am.managedUsers {
		result[userID] = &AIUserState{
			UserID:            state.UserID,
			StartTime:         state.StartTime,
			OperationCount:    state.OperationCount,
			LastOperationTime: state.LastOperationTime,
			IsActive:          state.IsActive,
		}
	}
	return result
}

// Cleanup 清理AI托管状态
func (am *AIManager) Cleanup() {
	am.mutex.Lock()
	defer am.mutex.Unlock()

	for userID := range am.managedUsers {
		am.updateRoomUserAIStatus(userID, false)
	}
	am.managedUsers = make(map[string]*AIUserState)
}

// GetManagedUsers 获取所有托管用户ID列表
func (am *AIManager) GetManagedUsers() []string {
	am.mutex.RLock()
	defer am.mutex.RUnlock()

	users := make([]string, 0, len(am.managedUsers))
	for userID := range am.managedUsers {
		users = append(users, userID)
	}
	return users
}
