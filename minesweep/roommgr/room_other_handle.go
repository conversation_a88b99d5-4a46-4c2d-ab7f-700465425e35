package roommgr

import (
	"minesweep/common/logx"
	"minesweep/constvar"
	"minesweep/ecode"
	"minesweep/localmgr"
	"minesweep/model/common/request"
	"minesweep/model/common/response"
	"minesweep/usermgr"

	"github.com/mitchellh/mapstructure"
)

// OnUserSitDown 玩家请求坐下
func (slf *Room) OnUserSitDown(msg *request.PackMessage) {
	params := &request.SitDown{}
	err := mapstructure.Decode(msg.Data, params)
	if err != nil {
		return
	}

	if params.Pos < 0 || params.Pos >= slf.playerNum {
		logx.Infof("RoomID:%v userID:%v params err", slf.RoomID, msg.Ext.UserID)
		return
	}

	user := usermgr.GetInstance().GetUserById(msg.Ext.AppChannel, msg.Ext.AppID, msg.Ext.UserID)
	if user == nil {
		logx.Errorf("RoomID:%v userID:%v usermgr no user", slf.RoomID, msg.Ext.UserID)
		return
	}

	roomUser := slf.GetUser(user.UserID)
	if roomUser == nil {
		logx.Infof("RoomID:%v userID:%v room no user", slf.RoomID, user.UserID)
		user.SendMessage(msg.MsgID, ecode.ErrNotFoundUser, struct{}{})
		return
	}
	slf.UpdateLastMsg(roomUser)

	// 玩家已坐下或者当庄，不能再次请求坐下
	if roomUser.Pos >= 0 {
		logx.Infof("RoomID:%v userID:%v user have sit", slf.RoomID, user.UserID)
		user.SendMessage(msg.MsgID, ecode.ErrHaveSit, struct{}{})
		return
	}

	if len(slf.allPlayerInfo[params.Pos].UserID) > 0 {
		logx.Infof("RoomID:%v userID:%v pos not empty", slf.RoomID, user.UserID)
		user.SendMessage(msg.MsgID, ecode.ErrSitHaveUser, struct{}{})
		return
	}

	roomUser.Pos = params.Pos
	roomUser.UserStatus = constvar.UserStatusSit
	slf.allPlayerInfo[params.Pos].SetUser(roomUser)

	logx.Infof("RoomID:%v userID:%v sitDown success, pos:%v", slf.RoomID, user.UserID, params.Pos)
	slf.Broadcast(msg.MsgID, ecode.OK, &response.NoticeUserSitDown{
		UserID:   user.UserID,
		NickName: user.Nickname,
		Avatar:   user.Avatar,
		Pos:      params.Pos,
		Coin:     roomUser.Coin,
	})
}

// OnRobotSitDown 机器人请求坐下
func (slf *Room) OnRobotSitDown(msg *request.PackMessage) {
	params := &request.RobotSitDown{}
	err := mapstructure.Decode(msg.Data, params)
	if err != nil {
		return
	}

	roomUser := slf.GetUser(params.UserID)
	if roomUser != nil {
		logx.Infof("RoomID:%v userID:%v room have exist", slf.RoomID, params.UserID)
		return
	}

	var emptyPos []int
	for pos, v := range slf.allPlayerInfo {
		if pos > 0 && len(v.UserID) == 0 {
			emptyPos = append(emptyPos, pos)
		}
	}
	if len(emptyPos) == 0 {
		logx.Errorf("RoomID:%v userID:%v no emptyPos", slf.RoomID, params.UserID)
		return
	}

	// 给机器人找空座
	var randPos = emptyPos[slf.RandNum(len(emptyPos))]
	u := &RoomUser{
		NickName:     params.NickName,
		Avatar:       params.Avatar,
		UserID:       params.UserID,
		IdentityType: constvar.IdentityTypeRobot,
		RobotLevel:   constvar.RobotLevelEasy,
		UserStatus:   constvar.UserStatusSit,
		Pos:          randPos,
		Coin:         0,
	}

	// allUser操作加锁
	slf.Lock()
	slf.allUser[u.UserID] = u
	slf.Unlock()
	slf.allPlayerInfo[randPos].SetUser(u)

	logx.Infof("RoomID:%v userID:%v robotSitDown success, randPos:%v", slf.RoomID, params.UserID, randPos)
	slf.Broadcast(constvar.MsgTypeSitDown, ecode.OK, &response.NoticeUserSitDown{
		UserID:   u.UserID,
		NickName: u.NickName,
		Avatar:   u.Avatar,
		Pos:      u.Pos,
		Coin:     u.Coin,
	})
}

// OnUserOffline 玩家离线
func (slf *Room) OnUserOffline(msg *request.PackMessage) {
	roomUser := slf.GetUser(msg.Ext.UserID)
	if roomUser == nil {
		logx.Infof("RoomID:%v userID:%v room no user", slf.RoomID, msg.Ext.UserID)
		return
	}

	roomUser.IsOffline = true
	logx.Infof("RoomID:%v userID:%v offline success", slf.RoomID, msg.Ext.UserID)

	// 用户离线时不停止AI托管，保持托管状态让游戏继续进行
	// 这样用户重连后可以看到游戏进展，提供更好的用户体验
	if slf.aiManager != nil && slf.aiManager.IsUserAIManaged(msg.Ext.UserID) {
		logx.Infof("用户离线但保持AI托管状态 UserID:%s RoomID:%d", msg.Ext.UserID, slf.RoomID)
	}

	slf.Broadcast(msg.MsgID, ecode.OK, &response.NoticeByUserID{
		UserID: msg.Ext.UserID,
	})

	// 为方便测试，断线，直接解散房间
	// slf.ForceCloseRoom()
}

// OnUserLeave 玩家主动离开房间
func (slf *Room) OnUserLeave(msg *request.PackMessage) {
	params := &request.LeaveRoom{}
	err := mapstructure.Decode(msg.Data, params)
	if err != nil {
		return
	}

	user := usermgr.GetInstance().GetUserById(msg.Ext.AppChannel, msg.Ext.AppID, msg.Ext.UserID)
	if user == nil {
		logx.Errorf("RoomID:%v userID:%v usermgr no user", slf.RoomID, msg.Ext.UserID)
		return
	}

	roomUser := slf.GetUser(user.UserID)
	if roomUser == nil {
		logx.Infof("RoomID:%v userID:%v room no user", slf.RoomID, user.UserID)
		user.SendMessage(msg.MsgID, ecode.ErrNotFoundUser, struct{}{})
		return
	}

	// 语聊房屏蔽玩家离开房间
	if slf.voiceRoom != nil {
		logx.Infof("RoomID:%v userID:%v voiceRoom no leave", slf.RoomID, user.UserID)
		return
	}

	// 特殊处理关卡房间离开
	if slf.IsLevelMode {
		slf.handleLevelGameLeave(user, msg.MsgID)
		return
	}

	// 多人房间的离开逻辑
	if !params.IsConfirmLeave && slf.IsPlaying() {
		logx.Infof("RoomID:%v userID:%v playing, gameStatus:%v", slf.RoomID, user.UserID, slf.gameStatus)
		user.SendMessage(msg.MsgID, ecode.ErrUserPlaying, struct{}{})
		return
	}

	logx.Infof("RoomID:%v userID:%v leaveRoom success, IsConfirmLeave:%v", slf.RoomID, user.UserID, params.IsConfirmLeave)

	// 停止AI托管（玩家主动离开时不需要AI托管）
	if slf.aiManager != nil && slf.aiManager.IsUserAIManaged(user.UserID) {
		err := slf.aiManager.StopAIManagement(user.UserID)
		if err != nil {
			logx.Errorf("停止离开用户AI托管失败 UserID:%s Error:%v", user.UserID, err)
		} else {
			logx.Infof("玩家主动离开，AI托管已停止 UserID:%s RoomID:%d", user.UserID, slf.RoomID)
		}
	}

	// 标记用户离开状态（必须在广播前设置）
	roomUser.IsLeave = true

	// 发送玩家离开广播通知
	slf.broadcastPlayerLeave(user.UserID)

	// 2人房间特殊处理：主动退出立即判负并结束游戏
	if slf.playerNum == 2 && slf.IsPlaying() {
		slf.handleTwoPlayerRoomLeave(user.UserID)
	} else if slf.playerNum >= 3 && slf.IsPlaying() {
		// 多人房间检查是否全员退出
		if slf.checkAllPlayersLeft() {
			slf.handleAllPlayersLeave()
		}
	}

	// 清理用户本地状态
	localmgr.GetInstance().RmvLocalByRoomID(slf.appChannel, slf.appID, user.UserID, slf.RoomID)

	// 发送标准离开响应
	user.SendMessage(msg.MsgID, ecode.OK, &response.NoticeByUserID{
		UserID: user.UserID,
	})
}

// handleLevelGameLeave 处理关卡房间的离开逻辑
func (slf *Room) handleLevelGameLeave(user *usermgr.User, msgID string) {
	// 验证用户权限
	if slf.LevelUserID != user.UserID {
		user.SendMessage(msgID, ecode.ErrParams, map[string]interface{}{
			"message": "无权限操作此房间",
		})
		return
	}

	// 标记游戏结束（玩家主动退出视为失败）
	slf.IsGameEnded = true
	slf.GameResult = false

	// 构建响应
	response := map[string]interface{}{
		"levelId": slf.LevelID,
		"message": "已退出关卡游戏",
	}

	// 发送响应
	user.SendMessage(msgID, ecode.OK, response)

	// 清理游戏状态
	GetInstance().clearLevelGameState(slf.appChannel, slf.appID, user.UserID)

	// 清理用户的本地房间信息
	localmgr.GetInstance().RmvLocalByRoomID(slf.appChannel, slf.appID, user.UserID, slf.RoomID)

	// 立即删除房间
	GetInstance().RemoveRoom(slf.RoomID)

	logx.Infof("关卡游戏通过LeaveRoom退出 UserID:%s LevelID:%d RoomID:%d",
		user.UserID, slf.LevelID, slf.RoomID)
}

// OnViewerList 获取旁观者列表
func (slf *Room) OnViewerList(msg *request.PackMessage) {
	user := usermgr.GetInstance().GetUserById(msg.Ext.AppChannel, msg.Ext.AppID, msg.Ext.UserID)
	if user == nil {
		logx.Errorf("RoomID:%v userID:%v usermgr no user", slf.RoomID, msg.Ext.UserID)
		return
	}

	// 判断是否是该房间的玩家
	roomUser := slf.GetUser(user.UserID)
	if roomUser == nil {
		logx.Infof("RoomID:%v userID:%v room no user", slf.RoomID, msg.Ext.UserID)
		user.SendMessage(msg.MsgID, ecode.ErrNotFoundUser, struct{}{})
		return
	}

	// 返回游戏桌面信息
	var viewers []*response.ViewerUser
	var allUser = slf.GetAllUser()
	for _, v := range allUser {
		if v.Pos >= 0 {
			continue
		}
		viewer := &response.ViewerUser{
			UserID:   v.UserID,
			NickName: v.NickName,
			Avatar:   v.Avatar,
		}
		viewers = append(viewers, viewer)
	}

	user.SendMessage(constvar.MsgTypeViewerList, ecode.OK, &response.ViewerList{
		Viewers: viewers,
	})
}

func (slf *Room) OnBuyProduct(msg *request.PackMessage) {
	// 更新玩家金币
	slf.UpdateBalance(msg.Ext.UserID)
}

func (slf *Room) OnSetSkin(msg *request.PackMessage) {
	params := &request.SetSkin{}
	err := mapstructure.Decode(msg.Data, params)
	if err != nil {
		return
	}

	user := usermgr.GetInstance().GetUserById(msg.Ext.AppChannel, msg.Ext.AppID, msg.Ext.UserID)
	if user == nil {
		logx.Errorf("RoomID:%v userID:%v usermgr no user", slf.RoomID, msg.Ext.UserID)
		return
	}

	// 判断是否是该房间的玩家
	roomUser := slf.GetUser(user.UserID)
	if roomUser == nil {
		logx.Infof("RoomID:%v userID:%v room no user", slf.RoomID, user.UserID)
		return
	}

	productCfg := slf.channelCfg.GetProductConf(params.Id)
	if productCfg == nil {
		logx.Infof("RoomID:%v userID:%v room no productID:%v", slf.RoomID, user.UserID, params.Id)
		return
	}

	roomUser.SkinChessID = productCfg.ID
	logx.Infof("RoomID:%v userID:%v OnSetSkin success, skinChessID:%v", slf.RoomID, user.UserID, productCfg.ID)
	slf.Broadcast(msg.MsgID, ecode.OK, &response.NoticeSetSkin{
		UserID: user.UserID,
		Type:   params.Id.Type(),
		Id:     params.Id,
	})
}
