package roommgr

import (
	"minesweep/common/logx"
	"minesweep/common/safe"
	"minesweep/constvar"
	"minesweep/ecode"
	"minesweep/model/common/base"
	"minesweep/model/common/response"
	"sort"
)

// SwitchToFirstMove 切换到先手
func (slf *Room) SwitchToFirstMove() {
	logx.Infof("RoomID:%v SwitchToFirstMove", slf.RoomID)

	// 初始化先手玩家
	var isFirstTimes bool
	if len(slf.firstMoves) == 0 {
		for _, v := range slf.allUser {
			if v.Pos < 0 {
				continue
			}
			slf.firstMoves[v.UserID] = &base.FirstMove{UserID: v.UserID}
		}
		isFirstTimes = true
	}

	var rankList []int
	for i := 0; i < len(slf.firstMoves); i++ {
		rankList = append(rankList, i+1)
	}

	var unFinishList []*base.FirstMove       // 本轮先手掷骰子结果
	var unFinishPointMap = make(map[int]int) // 本轮先手的点数 => 本轮出现次数
	for _, v := range slf.firstMoves {
		// 已有排名的剔除出去
		if v.Rank > 0 {
			v.IsRollDice = false // 标记该玩家不参与本轮先手
			for i := len(rankList) - 1; i >= 0; i-- {
				if rankList[i] == v.Rank {
					rankList = append(rankList[:i], rankList[i+1:]...)
					break
				}
			}
			continue
		}

		v.IsRollDice = true
		v.DicePoint = slf.Rand(1, 6)
		unFinishList = append(unFinishList, v)
		unFinishPointMap[v.DicePoint]++
	}
	sort.Slice(unFinishList, func(i, j int) bool {
		return unFinishList[i].DicePoint > unFinishList[j].DicePoint
	})

	// 决定排名
	for i, v := range unFinishList {
		// 出现超过1次的，决定不了排名
		count := unFinishPointMap[v.DicePoint]
		if count > 1 {
			// 如果4个人，出现两个5点、两个6点，任何排名都不能决定(参考0713-10:01视频)
			continue
		}
		v.Rank = rankList[i]
	}

	// 重置所有人的先手动画为未结束
	slf.isFirstMoveEnd = false
	var countDown = 4 // 先手时间4秒
	if isFirstTimes {
		countDown += 4
	}
	slf.SetGameStatus(constvar.GameStatusFirstMove, countDown)

	if !isFirstTimes {
		var firstMoves []*base.FirstMove
		for _, v := range slf.firstMoves {
			firstMoves = append(firstMoves, v)
		}
		slf.Broadcast(constvar.MsgTypeFirstMove, ecode.OK, &response.NoticeFirstMove{FirstMoves: firstMoves})
	}
}

// SwitchToRollDice 切换到掷骰子
func (slf *Room) SwitchToRollDice(isFirstTimes bool) {
	// 检查回合道具是否过期
	slf.allPlayerInfo[slf.curTokenPos].checkRoundProps()

	effectProps := slf.getUserEffectProps(slf.allPlayerInfo[slf.curTokenPos].UserID)
	slf.Broadcast(constvar.MsgTypeRollDice, ecode.OK, &response.NoticeRollDice{
		UserID:      slf.allPlayerInfo[slf.curTokenPos].UserID,
		RollTimes:   slf.allPlayerInfo[slf.curTokenPos].RollTimes,
		ChessPos:    slf.allPlayerInfo[slf.curTokenPos].CurChessPos,
		EffectProps: effectProps,
	})

	var countDown = 6 // 掷骰子时间6秒
	if slf.allPlayerInfo[slf.curTokenPos].IsRobot() {
		if isFirstTimes {
			slf.timerTask.Add(3000, func() {
				slf.ProcessCheckRollDice(true)
			})
		} else {
			slf.timerTask.Add(slf.Rand(1200, 3000), func() {
				slf.ProcessCheckRollDice(true)
			})
		}
	} else if slf.IsOffline(slf.allPlayerInfo[slf.curTokenPos].UserID) {
		if isFirstTimes {
			slf.timerTask.Add(3000, func() {
				slf.ProcessCheckRollDice(true)
			})
		} else {
			slf.timerTask.Add(2000, func() {
				slf.ProcessCheckRollDice(true)
			})
		}
	} else if slf.allPlayerInfo[slf.curTokenPos].IsTimeout() {
		slf.timerTask.Add(3000, func() {
			slf.ProcessCheckRollDice(true)
		})
	}
	if isFirstTimes {
		countDown += 1
	}
	logx.Infof("RoomID:%v SwitchToRollDice, userID:%v", slf.RoomID, slf.allPlayerInfo[slf.curTokenPos].UserID)
	slf.SetGameStatus(constvar.GameStatusRollDice, countDown)
}

// SwitchToMoveChess 切换移动棋子
func (slf *Room) SwitchToMoveChess() {
	logx.Infof("RoomID:%v SwitchToMoveChess, userID:%v", slf.RoomID, slf.allPlayerInfo[slf.curTokenPos].UserID)
	slf.SetGameStatus(constvar.GameStatusMoveChess, 5) // 移动棋子时间5秒
}

// SwitchToChoiceProp 切换挑选道具
func (slf *Room) SwitchToChoiceProp() {
	logx.Infof("RoomID:%v SwitchToChoiceProp", slf.RoomID)
	var countDown = 6 // 挑选道具时间6秒
	if slf.allPlayerInfo[slf.curTokenPos].IsRobot() {
		slf.timerTask.Add(slf.Rand(1200, 2000), func() {
			slf.ProcessCheckChoiceProp(true)
		})
	} else if slf.IsOffline(slf.allPlayerInfo[slf.curTokenPos].UserID) {
		countDown = 1
	} else if slf.allPlayerInfo[slf.curTokenPos].IsTimeout() {
		countDown = 2
	}
	slf.SetGameStatus(constvar.GameStatusChoiceProp, countDown)
}

// SwitchToUseProp 切换使用道具
func (slf *Room) SwitchToUseProp(propType constvar.GameProp) {
	logx.Infof("RoomID:%v SwitchToUseProp, propType:%v", slf.RoomID, propType)
	var countDown = propType.Time()
	if propType == constvar.GamePropAdvancement {
		// 使用了前进的道具，机器人或者离线玩家缩短时间去选择点数
		if slf.allPlayerInfo[slf.curTokenPos].IsRobot() {
			slf.timerTask.Add(slf.Rand(2000, 3000), func() {
				slf.ProcessCheckUseProp(true)
			})
		} else if slf.IsOffline(slf.allPlayerInfo[slf.curTokenPos].UserID) {
			countDown = 2
		} else if slf.allPlayerInfo[slf.curTokenPos].IsTimeout() {
			countDown = 3
		}
	}
	slf.SetGameStatus(constvar.GameStatusUseProp, countDown)
}

// SwitchToSettlement 切换到大结算
func (slf *Room) SwitchToSettlement() {
	logx.Infof("RoomID:%v SwitchToSettlement", slf.RoomID)
	if slf.isSettled {
		logx.Infof("RoomID:%v have settled", slf.RoomID)
		return
	}

	// 执行结算逻辑（包含消息发送）
	slf.SetSettlement()

	// 确保结算消息发送完成后再清理资源
	logx.Infof("RoomID:%v 结算完成，开始清理房间资源", slf.RoomID)

	// 清空延迟任务
	slf.timerTask.Clear()

	// 设置游戏状态为0，停止定时器
	slf.SetGameStatus(0, 0) // 使用SetGameStatus确保statusFreshTime正确更新

	// 移除所有玩家
	var allUser = slf.GetAllUser()
	for _, v := range allUser {
		slf.RemoveUser(v.UserID)
	}

	// 移除房间
	safe.Go(func() {
		GetInstance().RemoveRoom(slf.RoomID)
		logx.Infof("房间结算关闭成功 RoomID:%v", slf.RoomID)
	})
}
