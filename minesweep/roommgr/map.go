package roommgr

import (
	"fmt"
	"math/rand"
	"minesweep/common/logx"
	"minesweep/constvar"
	"time"
)

// 扫雷游戏常量
const (
	MineMapWidth  = 8  // 地图宽度
	MineMapHeight = 8  // 地图高度
	MineCount     = 13 // 地雷数量

)

// MineBlock 扫雷方块结构（mapType=0专用）
type MineBlock struct {
	X             int      `json:"x"`             // x坐标(0-7)
	Y             int      `json:"y"`             // y坐标(0-7)
	IsMine        bool     `json:"isMine"`        // 是否是地雷
	IsRevealed    bool     `json:"isRevealed"`    // 是否已揭开
	IsMarked      bool     `json:"isMarked"`      // 是否被标记为地雷
	NeighborMines int      `json:"neighborMines"` // 周围地雷数量(0-8)
	Players       []string `json:"players"`       // 当前格子中的玩家ID列表
}

// MineMap 扫雷地图
type MineMap struct {
	MapType       constvar.MapType `json:"mapType"`       // 地图类型
	Width         int              `json:"width"`         // 地图宽度(8)
	Height        int              `json:"height"`        // 地图高度(8)
	MineCount     int              `json:"mineCount"`     // 地雷总数(12)
	Blocks        [][]MineBlock    `json:"blocks"`        // 地图块二维数组[y][x] (方格地图用)
	RevealedCount int              `json:"revealedCount"` // 已揭开的非地雷格子数
	RoomID        int64            `json:"roomId"`        // 房间ID（用于日志）

	// 六边形地图专用字段
	ValidHexes  []HexCoord            `json:"validHexes,omitempty"`  // 有效的六边形坐标
	HexBlocks   map[string]*MineBlock `json:"hexBlocks,omitempty"`   // 六边形方块映射 "q,r" -> Block
	NeighborMap map[string][]string   `json:"neighborMap,omitempty"` // 邻居映射表 "q,r" -> ["q1,r1", ...]
}

// HexCoord 六边形坐标
type HexCoord struct {
	Q int `json:"q"` // 六边形坐标Q
	R int `json:"r"` // 六边形坐标R
}

// String 返回坐标的字符串表示（用于映射键）
func (h HexCoord) String() string {
	return fmt.Sprintf("%d,%d", h.Q, h.R)
}

// Equal 检查两个坐标是否相等
func (h HexCoord) Equal(other HexCoord) bool {
	return h.Q == other.Q && h.R == other.R
}

// Add 坐标加法
func (h HexCoord) Add(other HexCoord) HexCoord {
	return HexCoord{Q: h.Q + other.Q, R: h.R + other.R}
}

// Distance 计算两个六边形坐标之间的距离
func (h HexCoord) Distance(other HexCoord) int {
	return (abs(h.Q-other.Q) + abs(h.Q+h.R-other.Q-other.R) + abs(h.R-other.R)) / 2
}

// abs 返回整数的绝对值
func abs(x int) int {
	if x < 0 {
		return -x
	}
	return x
}

// RoundAction 回合操作
type RoundAction struct {
	UserID    string `json:"userId"`    // 玩家ID
	X         int    `json:"x"`         // x坐标
	Y         int    `json:"y"`         // y坐标
	Action    int    `json:"action"`    // 操作类型(1-挖掘，2-标记地雷)
	Timestamp int64  `json:"timestamp"` // 操作时间戳
	Score     int    `json:"score"`     // 本次操作得分（回合结束时计算）
	HitMine   bool   `json:"hitMine"`   // 是否踩到地雷（用于排名统计）
}

// RevealedBlock 连锁展开揭示的方块信息
type RevealedBlock struct {
	X             int    `json:"x"`             // x坐标
	Y             int    `json:"y"`             // y坐标
	NeighborMines int    `json:"neighborMines"` // 周围地雷数量
	IsMine        bool   `json:"isMine"`        // 是否是地雷
	TriggerUserID string `json:"triggerUserId"` // 触发揭示的玩家ID
}

// FloodFillResult 连锁展开结果
type FloodFillResult struct {
	RevealedBlocks []RevealedBlock `json:"revealedBlocks"` // 揭示的方块列表
	TotalRevealed  int             `json:"totalRevealed"`  // 总共揭示的方块数
	TriggerUserID  string          `json:"triggerUserId"`  // 触发连锁展开的玩家ID
	TriggerX       int             `json:"triggerX"`       // 触发点X坐标
	TriggerY       int             `json:"triggerY"`       // 触发点Y坐标
}

// PlayerActionDisplay 玩家操作展示（第20秒展示阶段使用）
type PlayerActionDisplay struct {
	UserID string `json:"userId"` // 玩家ID
	X      int    `json:"x"`      // 操作坐标x
	Y      int    `json:"y"`      // 操作坐标y
	Action int    `json:"action"` // 操作类型(1-挖掘，2-标记)
}

// PlayerRoundResult 玩家回合结果（第25秒回合结束时使用）
type PlayerRoundResult struct {
	UserID        string `json:"userId"`        // 玩家ID
	X             int    `json:"x"`             // 操作坐标x
	Y             int    `json:"y"`             // 操作坐标y
	Action        int    `json:"action"`        // 操作类型(1-挖掘，2-标记)
	Score         int    `json:"score"`         // 本回合得分
	IsFirstChoice bool   `json:"isFirstChoice"` // 是否为首选玩家
	IsMine        bool   `json:"isMine"`        // 是否是地雷
	NeighborMines int    `json:"neighborMines"` // 周围地雷数量
}

// PlayerFinalResult 玩家最终结果
type PlayerFinalResult struct {
	UserID     string `json:"userId"`     // 玩家ID
	TotalScore int    `json:"totalScore"` // 总得分（保留用于排名计算）
	TotalCoin  int64  `json:"totalCoin"`  // 获取到的金币（显示用）
	MineHits   int    `json:"mineHits"`   // 踩雷数
	Rank       int    `json:"rank"`       // 最终排名
	CoinChg    int64  `json:"coinChg"`    // 金币变化（结算用）
}

// PlayerFirstChoiceBonus 首选玩家奖励推送
type PlayerFirstChoiceBonus struct {
	UserID      string `json:"userId"`      // 玩家ID
	RoundNumber int    `json:"roundNumber"` // 回合编号
	BonusScore  int    `json:"bonusScore"`  // 首选玩家奖励分数（固定+1）
	TotalScore  int    `json:"totalScore"`  // 累计总得分（包含此奖励）
}

// NewMineMap 创建新的扫雷地图（默认方格地图）
func NewMineMap(roomID int64) *MineMap {
	return NewMineMapWithType(roomID, constvar.MapTypeGrid)
}

// NewMineMapWithType 创建指定类型的扫雷地图
func NewMineMapWithType(roomID int64, mapType constvar.MapType) *MineMap {
	mineMap := &MineMap{
		MapType:       mapType,
		RevealedCount: 0,
		RoomID:        roomID,
	}

	switch mapType {
	case constvar.MapTypeHexagon:
		// 六边形地图初始化
		mineMap.initHexagonMap()
	default:
		// 方格地图初始化
		mineMap.initGridMap()
	}

	return mineMap
}

// NewMineMapWithSize 创建指定尺寸的方格地图（关卡模式专用）
func NewMineMapWithSize(roomID int64, mapType constvar.MapType, width, height, mineCount int) *MineMap {
	mineMap := &MineMap{
		MapType:       mapType,
		Width:         width,
		Height:        height,
		MineCount:     mineCount,
		RevealedCount: 0,
		RoomID:        roomID,
	}

	// 初始化方格地图
	mineMap.initGridMapWithSize(width, height, mineCount)

	return mineMap
}

// NewMineMapWithHexID 创建指定六边形地图ID的地图（关卡模式专用）
func NewMineMapWithHexID(roomID int64, mapType constvar.MapType, hexMapID int) *MineMap {
	mineMap := &MineMap{
		MapType:       mapType,
		RevealedCount: 0,
		RoomID:        roomID,
	}

	// 初始化关卡专用六边形地图
	mineMap.initLevelHexagonMapWithID(hexMapID)

	return mineMap
}

// initGridMap 初始化方格地图
func (m *MineMap) initGridMap() {
	const (
		mapWidth  = 8  // 固定8×8地图
		mapHeight = 8  // 固定8×8地图
		mineCount = 12 // 固定12个地雷
	)

	m.Width = mapWidth
	m.Height = mapHeight
	m.MineCount = mineCount
	m.Blocks = make([][]MineBlock, mapHeight)

	// 初始化地图块（直接使用数组索引作为游戏坐标）
	for gameY := 0; gameY < mapHeight; gameY++ {
		m.Blocks[gameY] = make([]MineBlock, mapWidth)
		for gameX := 0; gameX < mapWidth; gameX++ {
			// 直接使用数组索引作为游戏坐标，无需转换
			m.Blocks[gameY][gameX] = MineBlock{
				X:             gameX,
				Y:             gameY, // 直接使用数组索引作为游戏坐标
				IsMine:        false,
				IsRevealed:    false,
				IsMarked:      false,
				NeighborMines: 0,
				Players:       make([]string, 0),
			}
		}
	}

	// 生成地雷分布
	m.generateMines()

	// 计算每个格子周围的地雷数量
	m.calculateNeighborMines()

	logx.Infof("方格地图创建成功 RoomID:%v Size:%dx%d MineCount:%d", m.RoomID, mapWidth, mapHeight, mineCount)
}

// initGridMapWithSize 初始化指定尺寸的方格地图（关卡模式专用）
func (m *MineMap) initGridMapWithSize(width, height, mineCount int) {
	m.Width = width
	m.Height = height
	m.MineCount = mineCount
	m.Blocks = make([][]MineBlock, height)

	// 初始化地图块
	for gameY := 0; gameY < height; gameY++ {
		m.Blocks[gameY] = make([]MineBlock, width)
		for gameX := 0; gameX < width; gameX++ {
			m.Blocks[gameY][gameX] = MineBlock{
				X:             gameX,
				Y:             gameY,
				IsMine:        false,
				IsRevealed:    false,
				IsMarked:      false,
				NeighborMines: 0,
				Players:       make([]string, 0),
			}
		}
	}

	// 生成地雷分布
	m.generateMines()

	// 计算每个格子周围的地雷数量
	m.calculateNeighborMines()

	logx.Infof("关卡方格地图创建成功 RoomID:%v Size:%dx%d MineCount:%d", m.RoomID, width, height, mineCount)
}

// initHexagonMap 初始化六边形地图
func (m *MineMap) initHexagonMap() {
	m.initHexagonMapWithID(0) // 使用指定地图，0为默认联机地图
}

// initHexagonMapWithID 使用指定地图ID初始化六边形地图
func (m *MineMap) initHexagonMapWithID(mapID int) {
	// 获取地图配置
	config := getHexMapConfigByID(mapID)

	m.ValidHexes = config.ValidHexes
	m.MineCount = config.MineCount

	m.HexBlocks = make(map[string]*MineBlock)
	m.NeighborMap = make(map[string][]string)

	// 初始化六边形方块
	for _, hex := range m.ValidHexes {
		coordKey := fmt.Sprintf("%d,%d", hex.Q, hex.R)
		m.HexBlocks[coordKey] = &MineBlock{
			X:             hex.Q, // 使用Q作为X坐标
			Y:             hex.R, // 使用R作为Y坐标
			IsMine:        false,
			IsRevealed:    false,
			IsMarked:      false,
			NeighborMines: 0,
			Players:       make([]string, 0),
		}
	}

	// 邻居关系处理：如果JSON中有预计算的邻居关系，直接使用；否则重新计算
	if len(config.NeighborMap) > 0 {
		m.loadPrecomputedNeighbors(config.NeighborMap)
	} else {
		m.precomputeHexNeighbors()
	}

	// 生成地雷分布
	if err := m.generateHexMines(); err != nil {
		logx.Errorf("六边形地雷生成失败 RoomID:%v Error:%v", m.RoomID, err)
		return // 初始化失败，直接返回
	}

	// 计算每个格子周围的地雷数量
	m.calculateHexNeighborMines()

	logx.Infof("六边形地图创建成功 RoomID:%v MapID:%d MapName:%s HexCount:%d MineCount:%d",
		m.RoomID, config.MapID, config.MapName, len(m.ValidHexes), m.MineCount)
}

// initLevelHexagonMapWithID 使用关卡专用六边形地图配置初始化地图
func (m *MineMap) initLevelHexagonMapWithID(hexMapID int) {
	// 获取关卡专用地图配置
	config := GetLevelHexMapConfigByID(hexMapID)
	if config == nil {
		logx.Errorf("关卡六边形地图配置获取失败 RoomID:%v HexMapID:%d", m.RoomID, hexMapID)
		return
	}

	m.ValidHexes = config.ValidHexes
	m.MineCount = config.MineCount

	m.HexBlocks = make(map[string]*MineBlock)
	m.NeighborMap = make(map[string][]string)

	// 初始化六边形方块
	for _, hex := range m.ValidHexes {
		coordKey := fmt.Sprintf("%d,%d", hex.Q, hex.R)
		m.HexBlocks[coordKey] = &MineBlock{
			X:             hex.Q, // 使用Q作为X坐标
			Y:             hex.R, // 使用R作为Y坐标
			IsMine:        false,
			IsRevealed:    false,
			IsMarked:      false,
			NeighborMines: 0,
			Players:       make([]string, 0),
		}
	}

	// 邻居关系处理：如果配置中有预计算的邻居关系，直接使用；否则重新计算
	if len(config.NeighborMap) > 0 {
		m.loadPrecomputedNeighbors(config.NeighborMap)
	} else {
		m.precomputeHexNeighbors()
	}

	// 生成地雷分布
	if err := m.generateHexMines(); err != nil {
		logx.Errorf("关卡六边形地雷生成失败 RoomID:%v Error:%v", m.RoomID, err)
		return // 初始化失败，直接返回
	}

	// 计算每个格子周围的地雷数量
	m.calculateHexNeighborMines()

	logx.Infof("关卡六边形地图创建成功 RoomID:%v HexMapID:%d HexCount:%d MineCount:%d",
		m.RoomID, hexMapID, len(m.ValidHexes), m.MineCount)
}

// generateMines 生成地雷分布（改进的软约束算法）
func (m *MineMap) generateMines() {
	// 使用改进的地雷分布算法
	minePositions := m.generateImprovedMineDistribution()

	// 在选定位置放置地雷
	for _, pos := range minePositions {
		gameX := pos % m.Width
		gameY := pos / m.Width
		m.Blocks[gameY][gameX].IsMine = true
	}

	logx.Infof("改进地雷分布生成完成 RoomID:%v MinePositions:%v", m.RoomID, minePositions)
}

// generateImprovedMineDistribution 改进的地雷分布算法
// 使用"软约束"方法：在保持随机性的同时，减少过度聚集
func (m *MineMap) generateImprovedMineDistribution() []int {
	totalPositions := m.Width * m.Height

	// 配置参数
	config := MineDistributionConfig{
		MaxAttempts:         100, // 最大尝试次数
		ClusterPenalty:      0.3, // 聚集惩罚权重（0-1）
		MinDistanceWeight:   0.2, // 最小距离权重（0-1）
		RegionBalanceWeight: 0.1, // 区域平衡权重（0-1）
	}

	// 尝试生成改进的分布
	for attempt := 0; attempt < config.MaxAttempts; attempt++ {
		positions := m.generateCandidateDistribution(totalPositions)

		// 评估分布质量
		quality := m.evaluateDistributionQuality(positions, config)

		// 如果质量足够好，或者是最后一次尝试，就使用这个分布
		if quality >= 0.7 || attempt == config.MaxAttempts-1 {
			logx.Infof("地雷分布质量评估 RoomID:%v Attempt:%d Quality:%.3f",
				m.RoomID, attempt+1, quality)
			return positions
		}
	}

	// 理论上不会到达这里，但作为安全保障
	return m.generateCandidateDistribution(totalPositions)
}

// MineDistributionConfig 地雷分布配置
type MineDistributionConfig struct {
	MaxAttempts         int     // 最大尝试次数
	ClusterPenalty      float64 // 聚集惩罚权重
	MinDistanceWeight   float64 // 最小距离权重
	RegionBalanceWeight float64 // 区域平衡权重
}

// generateCandidateDistribution 生成候选地雷分布
func (m *MineMap) generateCandidateDistribution(totalPositions int) []int {
	// 生成所有可能的位置
	positions := make([]int, totalPositions)
	for i := 0; i < totalPositions; i++ {
		positions[i] = i
	}

	// 随机打乱位置数组（保持基础随机性）
	rand.Shuffle(totalPositions, func(i, j int) {
		positions[i], positions[j] = positions[j], positions[i]
	})

	// 取前MineCount个位置作为候选地雷位置
	return positions[:m.MineCount]
}

// evaluateDistributionQuality 评估地雷分布质量
// 返回值范围：0.0-1.0，越高表示分布质量越好
func (m *MineMap) evaluateDistributionQuality(minePositions []int, config MineDistributionConfig) float64 {
	if len(minePositions) == 0 {
		return 0.0
	}

	var totalScore float64 = 1.0 // 基础分数

	// 1. 聚集惩罚：检查相邻地雷数量
	clusterScore := m.calculateClusterScore(minePositions)
	totalScore -= config.ClusterPenalty * (1.0 - clusterScore)

	// 2. 最小距离评分：避免地雷过于接近
	minDistanceScore := m.calculateMinDistanceScore(minePositions)
	totalScore -= config.MinDistanceWeight * (1.0 - minDistanceScore)

	// 3. 区域平衡评分：确保地雷在各区域相对均匀分布
	regionBalanceScore := m.calculateRegionBalanceScore(minePositions)
	totalScore -= config.RegionBalanceWeight * (1.0 - regionBalanceScore)

	// 确保分数在合理范围内
	if totalScore < 0.0 {
		totalScore = 0.0
	}
	if totalScore > 1.0 {
		totalScore = 1.0
	}

	return totalScore
}

// calculateClusterScore 计算聚集评分（越高越好）
func (m *MineMap) calculateClusterScore(minePositions []int) float64 {
	if len(minePositions) <= 1 {
		return 1.0
	}

	// 将位置转换为坐标
	mineCoords := make([][2]int, len(minePositions))
	for i, pos := range minePositions {
		mineCoords[i] = [2]int{pos % m.Width, pos / m.Width}
	}

	// 计算相邻地雷对的数量
	adjacentPairs := 0
	totalPairs := 0

	for i := 0; i < len(mineCoords); i++ {
		for j := i + 1; j < len(mineCoords); j++ {
			totalPairs++
			x1, y1 := mineCoords[i][0], mineCoords[i][1]
			x2, y2 := mineCoords[j][0], mineCoords[j][1]

			// 检查是否相邻（8方向）
			dx := abs(x1 - x2)
			dy := abs(y1 - y2)
			if dx <= 1 && dy <= 1 && (dx != 0 || dy != 0) {
				adjacentPairs++
			}
		}
	}

	// 相邻对越少，聚集评分越高
	if totalPairs == 0 {
		return 1.0
	}

	adjacentRatio := float64(adjacentPairs) / float64(totalPairs)
	return 1.0 - adjacentRatio // 相邻比例越低，评分越高
}

// calculateMinDistanceScore 计算最小距离评分（越高越好）
func (m *MineMap) calculateMinDistanceScore(minePositions []int) float64 {
	if len(minePositions) <= 1 {
		return 1.0
	}

	// 将位置转换为坐标
	mineCoords := make([][2]int, len(minePositions))
	for i, pos := range minePositions {
		mineCoords[i] = [2]int{pos % m.Width, pos / m.Width}
	}

	// 找到最小距离
	minDistance := float64(m.Width + m.Height) // 初始化为最大可能距离
	for i := 0; i < len(mineCoords); i++ {
		for j := i + 1; j < len(mineCoords); j++ {
			x1, y1 := mineCoords[i][0], mineCoords[i][1]
			x2, y2 := mineCoords[j][0], mineCoords[j][1]

			// 计算曼哈顿距离
			distance := float64(abs(x1-x2) + abs(y1-y2))
			if distance < minDistance {
				minDistance = distance
			}
		}
	}

	// 理想的最小距离（根据地图大小和地雷数量）
	idealMinDistance := 2.0 // 期望地雷之间至少相距2格

	// 距离评分：实际最小距离越接近理想值，评分越高
	if minDistance >= idealMinDistance {
		return 1.0
	}
	return minDistance / idealMinDistance
}

// calculateRegionBalanceScore 计算区域平衡评分（越高越好）
func (m *MineMap) calculateRegionBalanceScore(minePositions []int) float64 {
	// 将地图分为4个区域（2x2网格）
	regionCounts := make([]int, 4)

	for _, pos := range minePositions {
		x := pos % m.Width
		y := pos / m.Width

		// 确定属于哪个区域
		regionX := 0
		if x >= m.Width/2 {
			regionX = 1
		}
		regionY := 0
		if y >= m.Height/2 {
			regionY = 1
		}

		regionIndex := regionY*2 + regionX
		regionCounts[regionIndex]++
	}

	// 计算理想的每个区域地雷数量
	idealPerRegion := float64(len(minePositions)) / 4.0

	// 计算各区域与理想值的偏差
	totalDeviation := 0.0
	for _, count := range regionCounts {
		deviation := absFloat(float64(count) - idealPerRegion)
		totalDeviation += deviation
	}

	// 偏差越小，评分越高
	maxPossibleDeviation := float64(len(minePositions)) // 最大可能偏差
	if maxPossibleDeviation == 0 {
		return 1.0
	}

	return 1.0 - (totalDeviation / maxPossibleDeviation)
}

// absFloat 计算浮点数绝对值
func absFloat(x float64) float64 {
	if x < 0 {
		return -x
	}
	return x
}

// calculateNeighborMines 计算每个格子周围的地雷数量
func (m *MineMap) calculateNeighborMines() {
	// 8个方向的偏移量（3×3邻域，不包括中心点）
	directions := [][]int{
		{-1, -1}, {0, -1}, {1, -1}, // 下方三个（Y-1）
		{-1, 0}, {1, 0}, // 左右两个
		{-1, 1}, {0, 1}, {1, 1}, // 上方三个（Y+1）
	}

	for gameY := 0; gameY < m.Height; gameY++ {
		for gameX := 0; gameX < m.Width; gameX++ {
			if m.Blocks[gameY][gameX].IsMine {
				continue // 地雷格子不需要计算周围地雷数
			}

			mineCount := 0
			// 检查8个方向的邻居
			for _, dir := range directions {
				// 计算邻居的游戏坐标
				neighborX := gameX + dir[0]
				neighborY := gameY + dir[1]

				// 检查邻居是否在地图范围内
				if neighborX >= 0 && neighborX < m.Width && neighborY >= 0 && neighborY < m.Height {
					// 直接访问邻居方块，无需转换
					if m.Blocks[neighborY][neighborX].IsMine {
						mineCount++
					}
				}
			}
			m.Blocks[gameY][gameX].NeighborMines = mineCount
		}
	}

	logx.Infof("周围地雷数量计算完成 RoomID:%v", m.RoomID)
}

// GetBlock 获取指定游戏坐标的方块
func (m *MineMap) GetBlock(x, y int) *MineBlock {
	switch m.MapType {
	case constvar.MapTypeHexagon:
		// 六边形地图：x=q, y=r
		coordKey := fmt.Sprintf("%d,%d", x, y)
		return m.HexBlocks[coordKey]
	default:
		// 方格地图
		if x < 0 || x >= m.Width || y < 0 || y >= m.Height {
			return nil
		}
		// 直接使用游戏坐标访问数组，无需转换
		return &m.Blocks[y][x]
	}
}

// IsValidPosition 检查坐标是否有效
func (m *MineMap) IsValidPosition(x, y int) bool {
	switch m.MapType {
	case constvar.MapTypeHexagon:
		// 六边形地图：检查坐标是否在有效范围内
		coordKey := fmt.Sprintf("%d,%d", x, y)
		_, exists := m.HexBlocks[coordKey]
		return exists
	default:
		// 方格地图
		return x >= 0 && x < m.Width && y >= 0 && y < m.Height
	}
}

// GetMinePositions 获取所有地雷位置（用于调试）
func (m *MineMap) GetMinePositions() [][]int {
	var minePositions [][]int
	switch m.MapType {
	case constvar.MapTypeHexagon:
		// 六边形地图：遍历HexBlocks
		for _, block := range m.HexBlocks {
			if block.IsMine {
				// 返回六边形坐标(q,r)
				minePositions = append(minePositions, []int{block.X, block.Y})
			}
		}
	default:
		// 方格地图：遍历Blocks数组
		for gameY := 0; gameY < m.Height; gameY++ {
			for gameX := 0; gameX < m.Width; gameX++ {
				if m.Blocks[gameY][gameX].IsMine {
					// 直接返回游戏坐标
					minePositions = append(minePositions, []int{gameX, gameY})
				}
			}
		}
	}
	return minePositions
}

// GetRevealedNonMineCount 获取已揭开的非地雷格子数量
func (m *MineMap) GetRevealedNonMineCount() int {
	count := 0
	switch m.MapType {
	case constvar.MapTypeHexagon:
		// 六边形地图：遍历HexBlocks
		for _, block := range m.HexBlocks {
			if block.IsRevealed && !block.IsMine {
				count++
			}
		}
	default:
		// 方格地图：遍历Blocks数组
		for arrayY := 0; arrayY < m.Height; arrayY++ {
			for x := 0; x < m.Width; x++ {
				if m.Blocks[arrayY][x].IsRevealed && !m.Blocks[arrayY][x].IsMine {
					count++
				}
			}
		}
	}
	return count
}

// IsGameComplete 检查游戏是否完成（所有非地雷格子都被揭开）
func (m *MineMap) IsGameComplete() bool {
	switch m.MapType {
	case constvar.MapTypeHexagon:
		// 六边形地图：总格子数 = 有效六边形数量
		totalNonMineBlocks := len(m.ValidHexes) - m.MineCount
		return m.GetRevealedNonMineCount() >= totalNonMineBlocks
	default:
		// 方格地图：总格子数 = 宽度 × 高度
		totalNonMineBlocks := m.Width*m.Height - m.MineCount
		return m.GetRevealedNonMineCount() >= totalNonMineBlocks
	}
}

// CalculateActionScore 计算单个操作的基础得分（不包含首选奖励）
func (m *MineMap) CalculateActionScore(action *RoundAction) int {
	// 验证坐标有效性
	if !m.IsValidPosition(action.X, action.Y) {
		logx.Errorf("计算得分时坐标无效 RoomID:%v x:%v y:%v", m.RoomID, action.X, action.Y)
		return 0
	}

	block := m.GetBlock(action.X, action.Y)
	if block == nil {
		logx.Errorf("计算得分时方块为空 RoomID:%v x:%v y:%v", m.RoomID, action.X, action.Y)
		return 0
	}

	if action.Action == 1 { // 挖掘操作
		if block.IsMine {
			return -12 // 挖到地雷扣12分
		} else {
			return 6 // 挖到安全区得6分
		}
	} else if action.Action == 2 { // 标记操作
		if block.IsMine {
			return 10 // 正确标记地雷得10分
		} else {
			return 0 // 错误标记得0分
		}
	}

	logx.Errorf("未知操作类型 RoomID:%v UserID:%v Action:%v", m.RoomID, action.UserID, action.Action)
	return 0
}

// ProcessFloodFill 处理空白格连锁展开（广度优先搜索）
// 注意：此方法假设起始点已经被揭示，只处理连锁展开的邻居格子
func (m *MineMap) ProcessFloodFill(startX, startY int, triggerUserID string) *FloodFillResult {
	// 验证起始坐标
	if !m.IsValidPosition(startX, startY) {
		logx.Errorf("连锁展开起始坐标无效 RoomID:%v x:%v y:%v", m.RoomID, startX, startY)
		return &FloodFillResult{
			RevealedBlocks: []RevealedBlock{},
			TotalRevealed:  0,
			TriggerUserID:  triggerUserID,
			TriggerX:       startX,
			TriggerY:       startY,
		}
	}

	startBlock := m.GetBlock(startX, startY)
	if startBlock == nil {
		logx.Errorf("连锁展开起始方块不存在 RoomID:%v x:%v y:%v", m.RoomID, startX, startY)
		return &FloodFillResult{
			RevealedBlocks: []RevealedBlock{},
			TotalRevealed:  0,
			TriggerUserID:  triggerUserID,
			TriggerX:       startX,
			TriggerY:       startY,
		}
	}

	// 只有空白格（周围地雷数为0）才能触发连锁展开
	if startBlock.IsMine || startBlock.NeighborMines != 0 {
		return &FloodFillResult{
			RevealedBlocks: []RevealedBlock{},
			TotalRevealed:  0,
			TriggerUserID:  triggerUserID,
			TriggerX:       startX,
			TriggerY:       startY,
		}
	}

	switch m.MapType {
	case constvar.MapTypeHexagon:
		return m.processHexFloodFill(startX, startY, triggerUserID)
	default:
		return m.processGridFloodFill(startX, startY, triggerUserID)
	}
}

// processGridFloodFill 处理方格地图的连锁展开
func (m *MineMap) processGridFloodFill(startX, startY int, triggerUserID string) *FloodFillResult {
	// 8个方向的偏移量（3×3邻域，不包括中心点）
	directions := [][]int{
		{-1, -1}, {0, -1}, {1, -1}, // 下方三个（Y-1）
		{-1, 0}, {1, 0}, // 左右两个
		{-1, 1}, {0, 1}, {1, 1}, // 上方三个（Y+1）
	}

	var revealedBlocks []RevealedBlock
	visited := make(map[string]bool)   // 使用字符串键 "x,y" 来标记已访问的格子
	queue := [][]int{{startX, startY}} // BFS队列

	// 标记起始点为已访问（但不重复揭示）
	startKey := fmt.Sprintf("%d,%d", startX, startY)
	visited[startKey] = true

	for len(queue) > 0 {
		// 取出队列头部
		current := queue[0]
		queue = queue[1:]

		x, y := current[0], current[1]

		// 将8个方向的邻居加入处理队列
		for _, dir := range directions {
			// 计算邻居的游戏坐标
			nx, ny := x+dir[0], y+dir[1]
			neighborKey := fmt.Sprintf("%d,%d", nx, ny)

			// 跳过已访问的格子和无效坐标
			if visited[neighborKey] || !m.IsValidPosition(nx, ny) {
				continue
			}

			visited[neighborKey] = true

			// 直接使用游戏坐标访问数组，无需转换
			neighborBlock := &m.Blocks[ny][nx]

			// 跳过地雷和已揭示的格子
			if neighborBlock.IsMine || neighborBlock.IsRevealed {
				continue
			}

			// 揭示当前邻居格子
			neighborBlock.IsRevealed = true
			m.RevealedCount++

			// 记录揭示的方块信息（使用游戏坐标）
			revealedBlock := RevealedBlock{
				X:             nx,
				Y:             ny,
				NeighborMines: neighborBlock.NeighborMines,
				IsMine:        neighborBlock.IsMine,
				TriggerUserID: triggerUserID,
			}
			revealedBlocks = append(revealedBlocks, revealedBlock)

			// 如果这个邻居也是空白格，将其加入队列继续扩展
			if neighborBlock.NeighborMines == 0 {
				queue = append(queue, []int{nx, ny})
			}
		}
	}

	result := &FloodFillResult{
		RevealedBlocks: revealedBlocks,
		TotalRevealed:  len(revealedBlocks),
		TriggerUserID:  triggerUserID,
		TriggerX:       startX,
		TriggerY:       startY,
	}

	logx.Infof("方格地图连锁展开完成 RoomID:%v TriggerUser:%v StartPos:(%d,%d) RevealedCount:%d",
		m.RoomID, triggerUserID, startX, startY, len(revealedBlocks))

	return result
}

// processHexFloodFill 处理六边形地图的连锁展开
func (m *MineMap) processHexFloodFill(startX, startY int, triggerUserID string) *FloodFillResult {
	var revealedBlocks []RevealedBlock
	visited := make(map[string]bool)
	queue := []string{fmt.Sprintf("%d,%d", startX, startY)} // BFS队列，使用坐标字符串

	// 标记起始点为已访问（但不重复揭示）
	startKey := fmt.Sprintf("%d,%d", startX, startY)
	visited[startKey] = true

	for len(queue) > 0 {
		// 取出队列头部
		current := queue[0]
		queue = queue[1:]

		// 获取当前坐标的邻居
		neighbors := m.NeighborMap[current]

		for _, neighborKey := range neighbors {
			// 跳过已访问的格子
			if visited[neighborKey] {
				continue
			}

			visited[neighborKey] = true

			neighborBlock := m.HexBlocks[neighborKey]
			if neighborBlock == nil {
				continue
			}

			// 跳过地雷和已揭示的格子
			if neighborBlock.IsMine || neighborBlock.IsRevealed {
				continue
			}

			// 揭示当前邻居格子
			neighborBlock.IsRevealed = true
			m.RevealedCount++

			// 记录揭示的方块信息
			revealedBlock := RevealedBlock{
				X:             neighborBlock.X,
				Y:             neighborBlock.Y,
				NeighborMines: neighborBlock.NeighborMines,
				IsMine:        neighborBlock.IsMine,
				TriggerUserID: triggerUserID,
			}
			revealedBlocks = append(revealedBlocks, revealedBlock)

			// 如果这个邻居也是空白格，将其加入队列继续扩展
			if neighborBlock.NeighborMines == 0 {
				queue = append(queue, neighborKey)
			}
		}
	}

	result := &FloodFillResult{
		RevealedBlocks: revealedBlocks,
		TotalRevealed:  len(revealedBlocks),
		TriggerUserID:  triggerUserID,
		TriggerX:       startX,
		TriggerY:       startY,
	}

	logx.Infof("六边形地图连锁展开完成 RoomID:%v TriggerUser:%v StartPos:(%d,%d) RevealedCount:%d",
		m.RoomID, triggerUserID, startX, startY, len(revealedBlocks))

	return result
}

// PrintMapOverview 打印地图全貌（用于调试）
func (m *MineMap) PrintMapOverview(title string) {
	switch m.MapType {
	case constvar.MapTypeHexagon:
		m.printHexMapOverview(title)
	default:
		m.printGridMapOverview(title)
	}
}

// printGridMapOverview 打印方格地图全貌
func (m *MineMap) printGridMapOverview(title string) {
	logx.Infof("=== %s - 方格地图全貌 RoomID:%v ===", title, m.RoomID)
	logx.Infof("地图大小: %dx%d, 地雷数量: %d, 已揭示: %d", m.Width, m.Height, m.MineCount, m.RevealedCount)

	// 打印坐标系统说明
	logx.Infof("图例: * = 已挖掘地雷, ● = 未发现地雷, 数字 = 周围地雷数, . = 未挖掘, M = 正确标记, X = 错误标记")

	// 从上往下打印（Y=7到Y=0），这样显示时Y轴从下往上
	for gameY := m.Height - 1; gameY >= 0; gameY-- {
		var rowStr string
		rowStr = fmt.Sprintf("Y%d: ", gameY)

		for gameX := 0; gameX < m.Width; gameX++ {
			block := m.GetBlock(gameX, gameY)
			if block == nil {
				rowStr += "? "
				continue
			}

			var symbol string
			if block.IsRevealed {
				if block.IsMine {
					symbol = "*" // 已揭示的地雷
				} else {
					symbol = fmt.Sprintf("%d", block.NeighborMines) // 已揭示的数字
				}
			} else {
				if block.IsMarked {
					if block.IsMine {
						symbol = "M" // 正确标记的地雷
					} else {
						symbol = "X" // 错误标记的安全区
					}
				} else {
					if block.IsMine {
						symbol = "●" // 未发现的地雷（调试用）
					} else {
						symbol = "." // 未揭示的安全区
					}
				}
			}
			rowStr += symbol + " "
		}
		logx.Infof("%s", rowStr)
	}

	// 打印X轴标识
	xAxisStr := "   "
	for x := 0; x < m.Width; x++ {
		xAxisStr += fmt.Sprintf("%d ", x)
	}
	logx.Infof(" %s← X轴", xAxisStr)

	// 打印地雷位置列表
	minePositions := m.GetMinePositions()
	var mineListStr string
	for i, pos := range minePositions {
		if i > 0 {
			mineListStr += ", "
		}
		mineListStr += fmt.Sprintf("(%d,%d)", pos[0], pos[1])
	}
	logx.Infof("地雷位置: %s", mineListStr)
	logx.Infof("=== 方格地图全貌结束 ===")
}

// printHexMapOverview 打印六边形地图全貌
func (m *MineMap) printHexMapOverview(title string) {
	logx.Infof("=== %s - 六边形地图全貌 RoomID:%v ===", title, m.RoomID)
	logx.Infof("六边形数量: %d, 地雷数量: %d, 已揭示: %d", len(m.ValidHexes), m.MineCount, m.RevealedCount)

	// 打印图例
	logx.Infof("图例: * = 已挖掘地雷, ● = 未发现地雷, 数字 = 周围地雷数, . = 未挖掘, M = 正确标记, X = 错误标记")

	// 创建六边形地图的可视化表示
	m.printHexMapGrid()

	// 打印紧凑的坐标列表
	m.printHexMapCompact()

	// 打印地雷位置列表
	var mineList []string
	for coordKey, block := range m.HexBlocks {
		if block.IsMine {
			mineList = append(mineList, coordKey)
		}
	}
	logx.Infof("地雷位置: %v", mineList)
	logx.Infof("=== 六边形地图全貌结束 ===")
}

// printHexMapGrid 打印六边形地图的网格表示
func (m *MineMap) printHexMapGrid() {
	// 找到坐标范围
	minQ, maxQ := 0, 0
	minR, maxR := 0, 0

	if len(m.ValidHexes) == 0 {
		logx.Infof("六边形地图为空")
		return
	}

	// 初始化范围
	minQ, maxQ = m.ValidHexes[0].Q, m.ValidHexes[0].Q
	minR, maxR = m.ValidHexes[0].R, m.ValidHexes[0].R

	for _, hex := range m.ValidHexes {
		if hex.Q < minQ {
			minQ = hex.Q
		}
		if hex.Q > maxQ {
			maxQ = hex.Q
		}
		if hex.R < minR {
			minR = hex.R
		}
		if hex.R > maxR {
			maxR = hex.R
		}
	}

	// 创建坐标映射
	hexMap := make(map[string]string)
	for _, hex := range m.ValidHexes {
		coordKey := fmt.Sprintf("%d,%d", hex.Q, hex.R)
		block := m.HexBlocks[coordKey]
		if block == nil {
			hexMap[coordKey] = "?"
			continue
		}

		var symbol string
		if block.IsRevealed {
			if block.IsMine {
				symbol = "*" // 已揭示的地雷
			} else {
				symbol = fmt.Sprintf("%d", block.NeighborMines) // 已揭示的数字
			}
		} else {
			if block.IsMarked {
				if block.IsMine {
					symbol = "M" // 正确标记的地雷
				} else {
					symbol = "X" // 错误标记的安全区
				}
			} else {
				if block.IsMine {
					symbol = "●" // 未发现的地雷（调试用）
				} else {
					symbol = "." // 未揭示的安全区
				}
			}
		}
		hexMap[coordKey] = symbol
	}

	logx.Infof("六边形地图网格表示 (Q轴: %d到%d, R轴: %d到%d):", minQ, maxQ, minR, maxR)

	// 创建一个更直观的六边形布局
	m.printHexMapVisual(hexMap, minQ, maxQ, minR, maxR)

	// 打印统计信息
	mineCount := 0
	revealedCount := 0
	markedCount := 0

	for _, block := range m.HexBlocks {
		if block.IsMine {
			mineCount++
		}
		if block.IsRevealed {
			revealedCount++
		}
		if block.IsMarked {
			markedCount++
		}
	}

	logx.Infof("统计: 总格子=%d, 地雷=%d, 已揭示=%d, 已标记=%d",
		len(m.ValidHexes), mineCount, revealedCount, markedCount)
}

// printHexMapCompact 打印紧凑的六边形地图信息
func (m *MineMap) printHexMapCompact() {
	// 按行分组显示坐标信息
	coordsByR := make(map[int][]string)

	for _, hex := range m.ValidHexes {
		coordKey := fmt.Sprintf("%d,%d", hex.Q, hex.R)
		block := m.HexBlocks[coordKey]
		if block == nil {
			continue
		}

		var symbol string
		if block.IsRevealed {
			if block.IsMine {
				symbol = "*" // 已揭示的地雷
			} else {
				symbol = fmt.Sprintf("%d", block.NeighborMines) // 已揭示的数字
			}
		} else {
			if block.IsMarked {
				if block.IsMine {
					symbol = "M" // 正确标记的地雷
				} else {
					symbol = "X" // 错误标记的安全区
				}
			} else {
				if block.IsMine {
					symbol = "●" // 未发现的地雷（调试用）
				} else {
					symbol = "." // 未揭示的安全区
				}
			}
		}

		coordInfo := fmt.Sprintf("(%d,%d):%s", hex.Q, hex.R, symbol)
		coordsByR[hex.R] = append(coordsByR[hex.R], coordInfo)
	}

	// 按R值排序打印
	logx.Infof("按行显示坐标信息:")
	for r := -10; r <= 10; r++ { // 假设R值在合理范围内
		if coords, exists := coordsByR[r]; exists {
			logx.Infof("  R=%d: %v", r, coords)
		}
	}
}

// printHexMapVisual 打印六边形地图的可视化表示
func (m *MineMap) printHexMapVisual(hexMap map[string]string, minQ, maxQ, minR, maxR int) {
	// 固定边框宽度，确保所有行都能对齐
	borderWidth := 70 // 使用固定宽度确保一致性

	// 创建顶部边框
	topBorder := "┌"
	for i := 0; i < borderWidth-2; i++ {
		topBorder += "─"
	}
	topBorder += "┐"
	logx.Infof("%s", topBorder)

	// 按R轴从小到大打印（从上到下）
	for r := minR; r <= maxR; r++ {
		var rowStr string = "│ "

		// 计算缩进：六边形网格的特殊布局
		indent := 0
		if len(m.ValidHexes) > 20 { // 大地图使用简单缩进
			indent = abs(r) % 2
		} else { // 小地图使用更精确的缩进
			centerR := (minR + maxR) / 2
			distFromCenter := abs(r - centerR)
			indent = distFromCenter
		}

		for i := 0; i < indent; i++ {
			rowStr += " "
		}

		// 按Q轴从小到大打印（从左到右）
		hasContent := false
		for q := minQ; q <= maxQ; q++ {
			coordKey := fmt.Sprintf("%d,%d", q, r)
			if symbol, exists := hexMap[coordKey]; exists {
				rowStr += fmt.Sprintf("%s ", symbol)
				hasContent = true
			} else {
				rowStr += "  " // 空位置
			}
		}

		// 只打印有内容的行
		if hasContent {
			// 使用 printf 格式化实现完美对齐
			rLabel := fmt.Sprintf("R=%d", r)

			// 计算内容部分（去掉开头的"│ "）
			content := rowStr[2:] // 去掉"│ "

			// 使用格式化字符串确保R标识右对齐到固定位置
			// %-55s 表示左对齐，占用55个字符位置
			alignedRow := fmt.Sprintf("│ %-55s %s │", content, rLabel)
			logx.Infof("%s", alignedRow)
		}
	}

	// 创建底部边框
	bottomBorder := "└"
	for i := 0; i < borderWidth-2; i++ {
		bottomBorder += "─"
	}
	bottomBorder += "┘"
	logx.Infof("%s", bottomBorder)

	// 打印Q轴标识
	var qAxisStr string = "   "
	for i := 0; i < abs((minR+maxR)/2); i++ {
		qAxisStr += " "
	}
	for q := minQ; q <= maxQ; q++ {
		qAxisStr += fmt.Sprintf("Q%d ", q)
	}
	logx.Infof("%s", qAxisStr)
}

// ========== 六边形地图相关方法 ==========

// HexMapConfig 六边形地图配置
type HexMapConfig struct {
	MapID       int                   `json:"mapId"`
	MapName     string                `json:"mapName"`
	ValidHexes  []HexCoord            `json:"validHexes"`
	MineCount   int                   `json:"suggestedMines"`
	NeighborMap map[string][]HexCoord `json:"neighborMap,omitempty"` // 保持原有类型，在加载时转换
}

// 预定义的地图配置
// 🔧 JSON配置位置1：可以直接将hex_map_editor.html生成的JSON配置添加到这里
// 示例：将工具生成的JSON转换为Go结构体格式
// 🔧 JSON配置位置：将hex_map_editor.html生成的JSON直接复制到这里
var hexMapConfigs = map[int]*HexMapConfig{
	0: {
		MapID:   0,
		MapName: "04",
		ValidHexes: []HexCoord{
			{Q: 0, R: 0},
			{Q: 1, R: -2},
			{Q: 1, R: -1},
			{Q: 1, R: 0},
			{Q: 2, R: -4},
			{Q: 2, R: -3},
			{Q: 2, R: -2},
			{Q: 2, R: -1},
			{Q: 2, R: 0},
			{Q: 3, R: -6},
			{Q: 3, R: -5},
			{Q: 3, R: -4},
			{Q: 3, R: -3},
			{Q: 3, R: -2},
			{Q: 3, R: -1},
			{Q: 3, R: 0},
			{Q: 4, R: -7},
			{Q: 4, R: -6},
			{Q: 4, R: -5},
			{Q: 4, R: -4},
			{Q: 4, R: -3},
			{Q: 4, R: -2},
			{Q: 4, R: -1},
			{Q: 4, R: 0},
			{Q: 5, R: -7},
			{Q: 5, R: -6},
			{Q: 5, R: -5},
			{Q: 5, R: -4},
			{Q: 5, R: -3},
			{Q: 5, R: -2},
			{Q: 5, R: -1},
			{Q: 5, R: 0},
			{Q: 6, R: -7},
			{Q: 6, R: -6},
			{Q: 6, R: -5},
			{Q: 6, R: -4},
			{Q: 6, R: -3},
			{Q: 6, R: -2},
			{Q: 6, R: -1},
			{Q: 6, R: 0},
			{Q: 7, R: -7},
			{Q: 7, R: -6},
			{Q: 7, R: -5},
			{Q: 7, R: -4},
			{Q: 7, R: -3},
			{Q: 7, R: -2},
			{Q: 7, R: -1},
			{Q: 7, R: 0},
			{Q: 8, R: -7},
			{Q: 8, R: -6},
			{Q: 8, R: -5},
			{Q: 8, R: -4},
			{Q: 8, R: -3},
			{Q: 8, R: -2},
			{Q: 9, R: -7},
			{Q: 9, R: -6},
			{Q: 9, R: -5},
			{Q: 9, R: -4},
			{Q: 10, R: -7},
			{Q: 10, R: -6},
		},
		MineCount: 12,
		NeighborMap: map[string][]HexCoord{
			"5,-5":  {{Q: 6, R: -5}, {Q: 6, R: -6}, {Q: 5, R: -6}, {Q: 4, R: -5}, {Q: 4, R: -4}, {Q: 5, R: -4}},
			"5,-6":  {{Q: 6, R: -6}, {Q: 6, R: -7}, {Q: 5, R: -7}, {Q: 4, R: -6}, {Q: 4, R: -5}, {Q: 5, R: -5}},
			"5,-7":  {{Q: 6, R: -7}, {Q: 4, R: -7}, {Q: 4, R: -6}, {Q: 5, R: -6}},
			"5,-4":  {{Q: 6, R: -4}, {Q: 6, R: -5}, {Q: 5, R: -5}, {Q: 4, R: -4}, {Q: 4, R: -3}, {Q: 5, R: -3}},
			"5,-3":  {{Q: 6, R: -3}, {Q: 6, R: -4}, {Q: 5, R: -4}, {Q: 4, R: -3}, {Q: 4, R: -2}, {Q: 5, R: -2}},
			"5,-2":  {{Q: 6, R: -2}, {Q: 6, R: -3}, {Q: 5, R: -3}, {Q: 4, R: -2}, {Q: 4, R: -1}, {Q: 5, R: -1}},
			"5,-1":  {{Q: 6, R: -1}, {Q: 6, R: -2}, {Q: 5, R: -2}, {Q: 4, R: -1}, {Q: 4, R: 0}, {Q: 5, R: 0}},
			"5,0":   {{Q: 6, R: 0}, {Q: 6, R: -1}, {Q: 5, R: -1}, {Q: 4, R: 0}},
			"4,-5":  {{Q: 5, R: -5}, {Q: 5, R: -6}, {Q: 4, R: -6}, {Q: 3, R: -5}, {Q: 3, R: -4}, {Q: 4, R: -4}},
			"4,-6":  {{Q: 5, R: -6}, {Q: 5, R: -7}, {Q: 4, R: -7}, {Q: 3, R: -6}, {Q: 3, R: -5}, {Q: 4, R: -5}},
			"4,-7":  {{Q: 5, R: -7}, {Q: 3, R: -6}, {Q: 4, R: -6}},
			"4,-4":  {{Q: 5, R: -4}, {Q: 5, R: -5}, {Q: 4, R: -5}, {Q: 3, R: -4}, {Q: 3, R: -3}, {Q: 4, R: -3}},
			"4,-3":  {{Q: 5, R: -3}, {Q: 5, R: -4}, {Q: 4, R: -4}, {Q: 3, R: -3}, {Q: 3, R: -2}, {Q: 4, R: -2}},
			"4,-2":  {{Q: 5, R: -2}, {Q: 5, R: -3}, {Q: 4, R: -3}, {Q: 3, R: -2}, {Q: 3, R: -1}, {Q: 4, R: -1}},
			"4,-1":  {{Q: 5, R: -1}, {Q: 5, R: -2}, {Q: 4, R: -2}, {Q: 3, R: -1}, {Q: 3, R: 0}, {Q: 4, R: 0}},
			"4,0":   {{Q: 5, R: 0}, {Q: 5, R: -1}, {Q: 4, R: -1}, {Q: 3, R: 0}},
			"3,-5":  {{Q: 4, R: -5}, {Q: 4, R: -6}, {Q: 3, R: -6}, {Q: 2, R: -4}, {Q: 3, R: -4}},
			"3,-6":  {{Q: 4, R: -6}, {Q: 4, R: -7}, {Q: 3, R: -5}},
			"3,-4":  {{Q: 4, R: -4}, {Q: 4, R: -5}, {Q: 3, R: -5}, {Q: 2, R: -4}, {Q: 2, R: -3}, {Q: 3, R: -3}},
			"3,-3":  {{Q: 4, R: -3}, {Q: 4, R: -4}, {Q: 3, R: -4}, {Q: 2, R: -3}, {Q: 2, R: -2}, {Q: 3, R: -2}},
			"3,-2":  {{Q: 4, R: -2}, {Q: 4, R: -3}, {Q: 3, R: -3}, {Q: 2, R: -2}, {Q: 2, R: -1}, {Q: 3, R: -1}},
			"3,-1":  {{Q: 4, R: -1}, {Q: 4, R: -2}, {Q: 3, R: -2}, {Q: 2, R: -1}, {Q: 2, R: 0}, {Q: 3, R: 0}},
			"3,0":   {{Q: 4, R: 0}, {Q: 4, R: -1}, {Q: 3, R: -1}, {Q: 2, R: 0}},
			"2,-4":  {{Q: 3, R: -4}, {Q: 3, R: -5}, {Q: 2, R: -3}},
			"2,-3":  {{Q: 3, R: -3}, {Q: 3, R: -4}, {Q: 2, R: -4}, {Q: 1, R: -2}, {Q: 2, R: -2}},
			"2,-2":  {{Q: 3, R: -2}, {Q: 3, R: -3}, {Q: 2, R: -3}, {Q: 1, R: -2}, {Q: 1, R: -1}, {Q: 2, R: -1}},
			"2,-1":  {{Q: 3, R: -1}, {Q: 3, R: -2}, {Q: 2, R: -2}, {Q: 1, R: -1}, {Q: 1, R: 0}, {Q: 2, R: 0}},
			"2,0":   {{Q: 3, R: 0}, {Q: 3, R: -1}, {Q: 2, R: -1}, {Q: 1, R: 0}},
			"1,-2":  {{Q: 2, R: -2}, {Q: 2, R: -3}, {Q: 1, R: -1}},
			"1,-1":  {{Q: 2, R: -1}, {Q: 2, R: -2}, {Q: 1, R: -2}, {Q: 1, R: 0}},
			"1,0":   {{Q: 2, R: 0}, {Q: 2, R: -1}, {Q: 1, R: -1}},
			"6,-5":  {{Q: 7, R: -5}, {Q: 7, R: -6}, {Q: 6, R: -6}, {Q: 5, R: -5}, {Q: 5, R: -4}, {Q: 6, R: -4}},
			"6,-6":  {{Q: 7, R: -6}, {Q: 7, R: -7}, {Q: 6, R: -7}, {Q: 5, R: -6}, {Q: 5, R: -5}, {Q: 6, R: -5}},
			"6,-7":  {{Q: 7, R: -7}, {Q: 5, R: -7}, {Q: 5, R: -6}, {Q: 6, R: -6}},
			"6,-4":  {{Q: 7, R: -4}, {Q: 7, R: -5}, {Q: 6, R: -5}, {Q: 5, R: -4}, {Q: 5, R: -3}, {Q: 6, R: -3}},
			"6,-3":  {{Q: 7, R: -3}, {Q: 7, R: -4}, {Q: 6, R: -4}, {Q: 5, R: -3}, {Q: 5, R: -2}, {Q: 6, R: -2}},
			"6,-2":  {{Q: 7, R: -2}, {Q: 7, R: -3}, {Q: 6, R: -3}, {Q: 5, R: -2}, {Q: 5, R: -1}, {Q: 6, R: -1}},
			"6,-1":  {{Q: 7, R: -1}, {Q: 7, R: -2}, {Q: 6, R: -2}, {Q: 5, R: -1}, {Q: 5, R: 0}, {Q: 6, R: 0}},
			"6,0":   {{Q: 7, R: 0}, {Q: 7, R: -1}, {Q: 6, R: -1}, {Q: 5, R: 0}},
			"7,-5":  {{Q: 8, R: -5}, {Q: 8, R: -6}, {Q: 7, R: -6}, {Q: 6, R: -5}, {Q: 6, R: -4}, {Q: 7, R: -4}},
			"7,-6":  {{Q: 8, R: -6}, {Q: 8, R: -7}, {Q: 7, R: -7}, {Q: 6, R: -6}, {Q: 6, R: -5}, {Q: 7, R: -5}},
			"7,-7":  {{Q: 8, R: -7}, {Q: 6, R: -7}, {Q: 6, R: -6}, {Q: 7, R: -6}},
			"7,-4":  {{Q: 8, R: -4}, {Q: 8, R: -5}, {Q: 7, R: -5}, {Q: 6, R: -4}, {Q: 6, R: -3}, {Q: 7, R: -3}},
			"7,-3":  {{Q: 8, R: -3}, {Q: 8, R: -4}, {Q: 7, R: -4}, {Q: 6, R: -3}, {Q: 6, R: -2}, {Q: 7, R: -2}},
			"7,-2":  {{Q: 8, R: -2}, {Q: 8, R: -3}, {Q: 7, R: -3}, {Q: 6, R: -2}, {Q: 6, R: -1}, {Q: 7, R: -1}},
			"7,-1":  {{Q: 8, R: -2}, {Q: 7, R: -2}, {Q: 6, R: -1}, {Q: 6, R: 0}, {Q: 7, R: 0}},
			"7,0":   {{Q: 7, R: -1}, {Q: 6, R: 0}},
			"8,-5":  {{Q: 9, R: -5}, {Q: 9, R: -6}, {Q: 8, R: -6}, {Q: 7, R: -5}, {Q: 7, R: -4}, {Q: 8, R: -4}},
			"8,-6":  {{Q: 9, R: -6}, {Q: 9, R: -7}, {Q: 8, R: -7}, {Q: 7, R: -6}, {Q: 7, R: -5}, {Q: 8, R: -5}},
			"8,-7":  {{Q: 9, R: -7}, {Q: 7, R: -7}, {Q: 7, R: -6}, {Q: 8, R: -6}},
			"8,-4":  {{Q: 9, R: -4}, {Q: 9, R: -5}, {Q: 8, R: -5}, {Q: 7, R: -4}, {Q: 7, R: -3}, {Q: 8, R: -3}},
			"8,-3":  {{Q: 9, R: -4}, {Q: 8, R: -4}, {Q: 7, R: -3}, {Q: 7, R: -2}, {Q: 8, R: -2}},
			"8,-2":  {{Q: 8, R: -3}, {Q: 7, R: -2}, {Q: 7, R: -1}},
			"9,-5":  {{Q: 10, R: -6}, {Q: 9, R: -6}, {Q: 8, R: -5}, {Q: 8, R: -4}, {Q: 9, R: -4}},
			"9,-6":  {{Q: 10, R: -6}, {Q: 10, R: -7}, {Q: 9, R: -7}, {Q: 8, R: -6}, {Q: 8, R: -5}, {Q: 9, R: -5}},
			"9,-7":  {{Q: 10, R: -7}, {Q: 8, R: -7}, {Q: 8, R: -6}, {Q: 9, R: -6}},
			"9,-4":  {{Q: 9, R: -5}, {Q: 8, R: -4}, {Q: 8, R: -3}},
			"10,-6": {{Q: 10, R: -7}, {Q: 9, R: -6}, {Q: 9, R: -5}},
			"10,-7": {{Q: 9, R: -7}, {Q: 9, R: -6}, {Q: 10, R: -6}},
		},
	},
}

// getHexMapConfigByID 根据地图ID获取完整的地图配置
func getHexMapConfigByID(mapID int) *HexMapConfig {
	if config, exists := hexMapConfigs[mapID]; exists {
		// 验证配置的完整性
		if err := validateHexMapConfig(config); err != nil {
			logx.Errorf("地图配置验证失败 MapID:%d Error:%v", mapID, err)
			return hexMapConfigs[0] // 返回默认配置
		}
		return config
	}

	logx.Warnf("地图ID不存在，使用默认配置 MapID:%d", mapID)
	return hexMapConfigs[0]
}

// GetHexMapConfigByID 根据ID获取六边形地图配置（用于关卡系统）
func GetHexMapConfigByID(hexMapID int) *HexMapConfig {
	if hexMapID < 0 || hexMapID >= len(hexMapConfigs) {
		logx.Warnf("六边形地图ID超出范围，使用默认配置 hexMapID:%d", hexMapID)
		return hexMapConfigs[0] // 返回默认配置
	}
	return hexMapConfigs[hexMapID]
}

// validateHexMapConfig 验证六边形地图配置的完整性
func validateHexMapConfig(config *HexMapConfig) error {
	if config == nil {
		return fmt.Errorf("配置为空")
	}

	if len(config.ValidHexes) == 0 {
		return fmt.Errorf("有效坐标列表为空")
	}

	if config.MineCount <= 0 {
		return fmt.Errorf("地雷数量必须大于0，当前值：%d", config.MineCount)
	}

	if config.MineCount >= len(config.ValidHexes) {
		return fmt.Errorf("地雷数量(%d)不能大于等于总格子数(%d)", config.MineCount, len(config.ValidHexes))
	}

	// 验证坐标的连通性
	if !isHexMapConnected(config.ValidHexes) {
		return fmt.Errorf("地图坐标不连通")
	}

	return nil
}

// isHexMapConnected 检查六边形地图是否连通
func isHexMapConnected(hexes []HexCoord) bool {
	if len(hexes) <= 1 {
		return true
	}

	// 构建坐标集合
	coordSet := make(map[string]bool)
	for _, hex := range hexes {
		coordSet[fmt.Sprintf("%d,%d", hex.Q, hex.R)] = true
	}

	// 六边形的6个邻居方向
	directions := []HexCoord{
		{Q: 1, R: 0}, {Q: 1, R: -1}, {Q: 0, R: -1},
		{Q: -1, R: 0}, {Q: -1, R: 1}, {Q: 0, R: 1},
	}

	// 从第一个坐标开始BFS
	visited := make(map[string]bool)
	queue := []HexCoord{hexes[0]}
	startKey := fmt.Sprintf("%d,%d", hexes[0].Q, hexes[0].R)
	visited[startKey] = true

	for len(queue) > 0 {
		current := queue[0]
		queue = queue[1:]

		// 检查6个邻居
		for _, dir := range directions {
			neighborQ := current.Q + dir.Q
			neighborR := current.R + dir.R
			neighborKey := fmt.Sprintf("%d,%d", neighborQ, neighborR)

			// 如果邻居在坐标集合中且未访问过
			if coordSet[neighborKey] && !visited[neighborKey] {
				visited[neighborKey] = true
				queue = append(queue, HexCoord{Q: neighborQ, R: neighborR})
			}
		}
	}

	// 检查是否所有坐标都被访问过
	return len(visited) == len(hexes)
}

// precomputeHexNeighbors 预计算六边形邻居关系
func (m *MineMap) precomputeHexNeighbors() {
	// 六边形的6个邻居方向
	directions := []HexCoord{
		{Q: 1, R: 0},  // 右
		{Q: 1, R: -1}, // 右上
		{Q: 0, R: -1}, // 左上
		{Q: -1, R: 0}, // 左
		{Q: -1, R: 1}, // 左下
		{Q: 0, R: 1},  // 右下
	}

	for _, hex := range m.ValidHexes {
		coordKey := fmt.Sprintf("%d,%d", hex.Q, hex.R)
		neighbors := make([]string, 0)

		for _, dir := range directions {
			neighborQ := hex.Q + dir.Q
			neighborR := hex.R + dir.R
			neighborKey := fmt.Sprintf("%d,%d", neighborQ, neighborR)

			// 检查邻居是否在有效范围内
			if _, exists := m.HexBlocks[neighborKey]; exists {
				neighbors = append(neighbors, neighborKey)
			}
		}

		m.NeighborMap[coordKey] = neighbors
	}
}

// loadPrecomputedNeighbors 从JSON配置加载预计算的邻居关系
func (m *MineMap) loadPrecomputedNeighbors(neighborMap map[string][]HexCoord) {
	// 将JSON格式的邻居关系转换为内部格式
	for coordKey, neighbors := range neighborMap {
		neighborKeys := make([]string, 0, len(neighbors))
		for _, neighbor := range neighbors {
			neighborKey := fmt.Sprintf("%d,%d", neighbor.Q, neighbor.R)
			// 只添加在有效范围内的邻居
			if _, exists := m.HexBlocks[neighborKey]; exists {
				neighborKeys = append(neighborKeys, neighborKey)
			}
		}
		m.NeighborMap[coordKey] = neighborKeys
	}

	logx.Infof("从JSON配置加载预计算邻居关系成功，共%d个坐标", len(neighborMap))
}

// generateHexMines 为六边形地图生成地雷（改进的软约束算法）
func (m *MineMap) generateHexMines() error {
	startTime := time.Now()
	defer func() {
		duration := time.Since(startTime)
		logx.Infof("六边形地雷生成耗时 RoomID:%v Duration:%v", m.RoomID, duration)
	}()

	// 验证前置条件
	if len(m.HexBlocks) == 0 {
		return fmt.Errorf("六边形方块为空，无法生成地雷")
	}

	if m.MineCount <= 0 {
		return fmt.Errorf("地雷数量必须大于0，当前值：%d", m.MineCount)
	}

	if m.MineCount >= len(m.HexBlocks) {
		return fmt.Errorf("地雷数量(%d)不能大于等于总格子数(%d)", m.MineCount, len(m.HexBlocks))
	}

	// 使用改进的六边形地雷分布算法
	selectedKeys := m.generateImprovedHexMineDistribution()

	// 设置选定位置为地雷
	mineCount := 0
	for _, key := range selectedKeys {
		if block := m.HexBlocks[key]; block != nil {
			block.IsMine = true
			mineCount++
		}
	}

	if mineCount != m.MineCount {
		logx.Warnf("实际生成地雷数与预期不符 RoomID:%v Expected:%d Actual:%d",
			m.RoomID, m.MineCount, mineCount)
	}

	logx.Infof("改进六边形地雷生成完成 RoomID:%v MineCount:%d TotalBlocks:%d",
		m.RoomID, mineCount, len(m.HexBlocks))

	return nil
}

// generateImprovedHexMineDistribution 改进的六边形地雷分布算法
func (m *MineMap) generateImprovedHexMineDistribution() []string {
	// 配置参数
	config := MineDistributionConfig{
		MaxAttempts:         50,   // 六边形地图尝试次数可以少一些
		ClusterPenalty:      0.4,  // 六边形地图聚集惩罚稍高
		MinDistanceWeight:   0.2,  // 最小距离权重
		RegionBalanceWeight: 0.15, // 区域平衡权重稍高
	}

	// 尝试生成改进的分布
	for attempt := 0; attempt < config.MaxAttempts; attempt++ {
		hexKeys := m.generateCandidateHexDistribution()

		// 评估分布质量
		quality := m.evaluateHexDistributionQuality(hexKeys, config)

		// 如果质量足够好，或者是最后一次尝试，就使用这个分布
		if quality >= 0.65 || attempt == config.MaxAttempts-1 {
			logx.Infof("六边形地雷分布质量评估 RoomID:%v Attempt:%d Quality:%.3f",
				m.RoomID, attempt+1, quality)
			return hexKeys
		}
	}

	// 理论上不会到达这里，但作为安全保障
	return m.generateCandidateHexDistribution()
}

// generateCandidateHexDistribution 生成候选六边形地雷分布
func (m *MineMap) generateCandidateHexDistribution() []string {
	// 使用新的随机数生成器
	rng := rand.New(rand.NewSource(time.Now().UnixNano()))

	// 预分配切片容量以提高性能
	hexKeys := make([]string, 0, len(m.HexBlocks))
	for key := range m.HexBlocks {
		hexKeys = append(hexKeys, key)
	}

	// 随机打乱（Fisher-Yates算法）
	for i := len(hexKeys) - 1; i > 0; i-- {
		j := rng.Intn(i + 1)
		hexKeys[i], hexKeys[j] = hexKeys[j], hexKeys[i]
	}

	// 取前MineCount个位置作为候选地雷位置
	return hexKeys[:m.MineCount]
}

// evaluateHexDistributionQuality 评估六边形地雷分布质量
func (m *MineMap) evaluateHexDistributionQuality(mineKeys []string, config MineDistributionConfig) float64 {
	if len(mineKeys) == 0 {
		return 0.0
	}

	var totalScore float64 = 1.0 // 基础分数

	// 1. 聚集惩罚：检查相邻地雷数量
	clusterScore := m.calculateHexClusterScore(mineKeys)
	totalScore -= config.ClusterPenalty * (1.0 - clusterScore)

	// 2. 最小距离评分：避免地雷过于接近
	minDistanceScore := m.calculateHexMinDistanceScore(mineKeys)
	totalScore -= config.MinDistanceWeight * (1.0 - minDistanceScore)

	// 3. 区域平衡评分：确保地雷在各区域相对均匀分布
	regionBalanceScore := m.calculateHexRegionBalanceScore(mineKeys)
	totalScore -= config.RegionBalanceWeight * (1.0 - regionBalanceScore)

	// 确保分数在合理范围内
	if totalScore < 0.0 {
		totalScore = 0.0
	}
	if totalScore > 1.0 {
		totalScore = 1.0
	}

	return totalScore
}

// calculateHexClusterScore 计算六边形聚集评分（越高越好）
func (m *MineMap) calculateHexClusterScore(mineKeys []string) float64 {
	if len(mineKeys) <= 1 {
		return 1.0
	}

	// 计算相邻地雷对的数量
	adjacentPairs := 0
	totalPairs := 0

	for i := 0; i < len(mineKeys); i++ {
		for j := i + 1; j < len(mineKeys); j++ {
			totalPairs++

			// 检查是否相邻（使用预计算的邻居关系）
			if neighbors, exists := m.NeighborMap[mineKeys[i]]; exists {
				for _, neighborKey := range neighbors {
					if neighborKey == mineKeys[j] {
						adjacentPairs++
						break
					}
				}
			}
		}
	}

	// 相邻对越少，聚集评分越高
	if totalPairs == 0 {
		return 1.0
	}

	adjacentRatio := float64(adjacentPairs) / float64(totalPairs)
	return 1.0 - adjacentRatio // 相邻比例越低，评分越高
}

// calculateHexMinDistanceScore 计算六边形最小距离评分（越高越好）
func (m *MineMap) calculateHexMinDistanceScore(mineKeys []string) float64 {
	if len(mineKeys) <= 1 {
		return 1.0
	}

	// 将坐标键转换为坐标
	mineCoords := make([][2]int, len(mineKeys))
	for i, key := range mineKeys {
		if block := m.HexBlocks[key]; block != nil {
			mineCoords[i] = [2]int{block.X, block.Y}
		}
	}

	// 找到最小距离（使用六边形距离计算）
	minDistance := float64(100) // 初始化为较大值
	for i := 0; i < len(mineCoords); i++ {
		for j := i + 1; j < len(mineCoords); j++ {
			q1, r1 := mineCoords[i][0], mineCoords[i][1]
			q2, r2 := mineCoords[j][0], mineCoords[j][1]

			// 六边形距离计算
			distance := float64(abs(q1-q2)+abs(q1+r1-q2-r2)+abs(r1-r2)) / 2.0
			if distance < minDistance {
				minDistance = distance
			}
		}
	}

	// 理想的最小距离
	idealMinDistance := 2.0 // 期望地雷之间至少相距2格

	// 距离评分：实际最小距离越接近理想值，评分越高
	if minDistance >= idealMinDistance {
		return 1.0
	}
	return minDistance / idealMinDistance
}

// calculateHexRegionBalanceScore 计算六边形区域平衡评分（越高越好）
func (m *MineMap) calculateHexRegionBalanceScore(mineKeys []string) float64 {
	// 根据六边形地图的特点，将其分为中心和外围区域
	centerRegionCount := 0
	outerRegionCount := 0

	for _, key := range mineKeys {
		if block := m.HexBlocks[key]; block != nil {
			q, r := block.X, block.Y

			// 简单的区域划分：距离原点较近的为中心区域
			distance := abs(q) + abs(q+r) + abs(r)
			if distance <= 2 {
				centerRegionCount++
			} else {
				outerRegionCount++
			}
		}
	}

	// 计算理想的区域分布（大约1:2的比例，外围区域更多）
	totalMines := len(mineKeys)
	idealCenterRatio := 0.3 // 30%在中心区域
	idealCenter := float64(totalMines) * idealCenterRatio
	idealOuter := float64(totalMines) * (1.0 - idealCenterRatio)

	// 计算偏差
	centerDeviation := absFloat(float64(centerRegionCount) - idealCenter)
	outerDeviation := absFloat(float64(outerRegionCount) - idealOuter)
	totalDeviation := centerDeviation + outerDeviation

	// 偏差越小，评分越高
	maxPossibleDeviation := float64(totalMines) // 最大可能偏差
	if maxPossibleDeviation == 0 {
		return 1.0
	}

	return 1.0 - (totalDeviation / maxPossibleDeviation)
}

// calculateHexNeighborMines 计算六边形地图每个格子周围的地雷数量
func (m *MineMap) calculateHexNeighborMines() {
	for coordKey, block := range m.HexBlocks {
		if block.IsMine {
			continue // 地雷本身不需要计算
		}

		mineCount := 0
		neighbors := m.NeighborMap[coordKey]

		for _, neighborKey := range neighbors {
			if neighborBlock, exists := m.HexBlocks[neighborKey]; exists && neighborBlock.IsMine {
				mineCount++
			}
		}

		block.NeighborMines = mineCount
	}
}
