package roommgr

import (
	"fmt"
	"math/rand"

	"minesweep/constvar"
)

// DecisionUtils 决策工具类，提供统一的随机决策和智能决策接口
type DecisionUtils struct {
	room           *Room
	decisionEngine *AIDecisionEngine
}

// NewDecisionUtils 创建决策工具实例
func NewDecisionUtils(room *Room) *DecisionUtils {
	return &DecisionUtils{
		room:           room,
		decisionEngine: NewAIDecisionEngine(room),
	}
}

// MakeRandomDecision 生成随机决策
func (du *DecisionUtils) MakeRandomDecision() (*AIOperation, error) {
	switch du.room.MapType {
	case constvar.MapTypeGrid:
		return du.makeRandomGridDecision()
	case constvar.MapTypeHexagon:
		return du.makeRandomHexDecision()
	default:
		return nil, fmt.Errorf("不支持的地图类型: %d", du.room.MapType)
	}
}

// MakeIntelligentDecision 生成智能决策
func (du *DecisionUtils) MakeIntelligentDecision(userID string) (*AIOperation, error) {
	return du.decisionEngine.MakeDecision(userID)
}

// MakeHybridDecision 生成混合决策（根据智能概率选择智能或随机）
func (du *DecisionUtils) MakeHybridDecision(userID string, intelligentProbability float32) (*AIOperation, error) {
	if rand.Float32() < intelligentProbability {
		// 尝试智能决策
		operation, err := du.MakeIntelligentDecision(userID)
		if err == nil && operation != nil {
			return operation, nil
		}
		// 智能决策失败，回退到随机决策
	}

	// 使用随机决策
	return du.MakeRandomDecision()
}

// makeRandomGridDecision 随机方格决策
func (du *DecisionUtils) makeRandomGridDecision() (*AIOperation, error) {
	availableBlocks := du.getAvailableGridBlocks()
	if len(availableBlocks) == 0 {
		return nil, fmt.Errorf("没有可操作的方格方块")
	}

	// 随机选择方块
	pos := availableBlocks[rand.Intn(len(availableBlocks))]

	// 随机操作倾向：70%挖掘，30%标记
	action := 1 // 挖掘
	if rand.Float32() < 0.3 {
		action = 2 // 标记
	}

	return &AIOperation{
		X:      pos.X,
		Y:      pos.Y,
		Action: action,
	}, nil
}

// makeRandomHexDecision 随机六边形决策
func (du *DecisionUtils) makeRandomHexDecision() (*AIOperation, error) {
	availableHexes := du.getAvailableHexBlocks()
	if len(availableHexes) == 0 {
		return nil, fmt.Errorf("没有可操作的六边形方块")
	}

	// 随机选择六边形方块
	pos := availableHexes[rand.Intn(len(availableHexes))]

	// 随机操作倾向：70%挖掘，30%标记
	action := 1 // 挖掘
	if rand.Float32() < 0.3 {
		action = 2 // 标记
	}

	return &AIOperation{
		Q:      pos.Q,
		R:      pos.R,
		Action: action,
	}, nil
}

// getAvailableGridBlocks 获取可操作的方格方块
func (du *DecisionUtils) getAvailableGridBlocks() []struct{ X, Y int } {
	var available []struct{ X, Y int }

	for y := 0; y < du.room.mineMap.Height; y++ {
		for x := 0; x < du.room.mineMap.Width; x++ {
			block := du.room.mineMap.GetBlock(x, y)
			if block != nil && !block.IsRevealed && !block.IsMarked {
				available = append(available, struct{ X, Y int }{X: x, Y: y})
			}
		}
	}

	return available
}

// getAvailableHexBlocks 获取可操作的六边形方块
func (du *DecisionUtils) getAvailableHexBlocks() []struct{ Q, R int } {
	var available []struct{ Q, R int }

	for _, hex := range du.room.mineMap.ValidHexes {
		coordKey := fmt.Sprintf("%d,%d", hex.Q, hex.R)
		block := du.room.mineMap.HexBlocks[coordKey]
		if block != nil && !block.IsRevealed && !block.IsMarked {
			available = append(available, struct{ Q, R int }{Q: hex.Q, R: hex.R})
		}
	}

	return available
}

// GetAvailableBlockCount 获取可操作方块数量
func (du *DecisionUtils) GetAvailableBlockCount() int {
	switch du.room.MapType {
	case constvar.MapTypeGrid:
		return len(du.getAvailableGridBlocks())
	case constvar.MapTypeHexagon:
		return len(du.getAvailableHexBlocks())
	default:
		return 0
	}
}

// IsValidOperation 检查操作是否有效
func (du *DecisionUtils) IsValidOperation(operation *AIOperation) bool {
	if operation == nil {
		return false
	}

	var block *MineBlock
	switch du.room.MapType {
	case constvar.MapTypeGrid:
		if operation.X < 0 || operation.X >= du.room.mineMap.Width ||
			operation.Y < 0 || operation.Y >= du.room.mineMap.Height {
			return false
		}
		block = du.room.mineMap.GetBlock(operation.X, operation.Y)
	case constvar.MapTypeHexagon:
		coordKey := fmt.Sprintf("%d,%d", operation.Q, operation.R)
		block = du.room.mineMap.HexBlocks[coordKey]
	default:
		return false
	}

	return block != nil && !block.IsRevealed && !block.IsMarked
}

// RobotDecisionLevel 机器人决策等级
type RobotDecisionLevel int

const (
	RobotDecisionEasy   RobotDecisionLevel = iota // 30%智能，70%随机
	RobotDecisionMedium                           // 60%智能，40%随机
	RobotDecisionHard                             // 90%智能，10%随机
)

// MakeRobotDecision 根据机器人等级生成决策
func (du *DecisionUtils) MakeRobotDecision(userID string, level RobotDecisionLevel) (*AIOperation, error) {
	var intelligentProbability float32

	switch level {
	case RobotDecisionEasy:
		intelligentProbability = 0.3 // 30%智能决策
	case RobotDecisionMedium:
		intelligentProbability = 0.6 // 60%智能决策
	case RobotDecisionHard:
		intelligentProbability = 0.9 // 90%智能决策
	default:
		intelligentProbability = 0.6 // 默认中等
	}

	return du.MakeHybridDecision(userID, intelligentProbability)
}

// ConvertRobotLevel 转换机器人等级到决策等级
func ConvertRobotLevel(robotLevel constvar.RobotLevel) RobotDecisionLevel {
	switch robotLevel {
	case constvar.RobotLevelEasy:
		return RobotDecisionEasy
	case constvar.RobotLevelHard:
		return RobotDecisionHard
	default:
		return RobotDecisionMedium
	}
}

// MakeRandomDigDecision 生成随机挖掘决策（仅限挖掘操作，不允许标记）
func (du *DecisionUtils) MakeRandomDigDecision() (*AIOperation, error) {
	switch du.room.MapType {
	case constvar.MapTypeGrid:
		return du.makeRandomGridDigDecision()
	case constvar.MapTypeHexagon:
		return du.makeRandomHexDigDecision()
	default:
		return nil, fmt.Errorf("不支持的地图类型: %d", du.room.MapType)
	}
}

// makeRandomGridDigDecision 随机方格挖掘决策（仅限挖掘操作）
func (du *DecisionUtils) makeRandomGridDigDecision() (*AIOperation, error) {
	availableBlocks := du.getAvailableGridBlocks()
	if len(availableBlocks) == 0 {
		return nil, fmt.Errorf("没有可操作的方格方块")
	}

	// 随机选择方块
	pos := availableBlocks[rand.Intn(len(availableBlocks))]

	// AI托管仅限挖掘操作
	return &AIOperation{
		X:      pos.X,
		Y:      pos.Y,
		Action: 1, // 固定为挖掘操作
	}, nil
}

// makeRandomHexDigDecision 随机六边形挖掘决策（仅限挖掘操作）
func (du *DecisionUtils) makeRandomHexDigDecision() (*AIOperation, error) {
	availableHexes := du.getAvailableHexBlocks()
	if len(availableHexes) == 0 {
		return nil, fmt.Errorf("没有可操作的六边形方块")
	}

	// 随机选择六边形方块
	pos := availableHexes[rand.Intn(len(availableHexes))]

	// AI托管仅限挖掘操作
	return &AIOperation{
		Q:      pos.Q,
		R:      pos.R,
		Action: 1, // 固定为挖掘操作
	}, nil
}
