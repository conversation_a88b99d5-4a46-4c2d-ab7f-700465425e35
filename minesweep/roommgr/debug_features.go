package roommgr

import (
	"fmt"
	"minesweep/common/logx"
	"minesweep/constvar"
	"minesweep/ecode"
	"minesweep/model/common/request"
	"minesweep/usermgr"
)

// ==================== 调试功能模块 ====================
// 注意：此文件包含调试接口，仅用于开发测试
// 生产环境应禁用或删除此文件中的功能

// OnDebugShowMines 调试接口：显示所有地雷位置
// 注意：这是调试接口，仅用于开发测试，生产环境应禁用
func (slf *Room) OnDebugShowMines(msg *request.PackMessage) {
	user := usermgr.GetInstance().GetUserById(msg.Ext.AppChannel, msg.Ext.AppID, msg.Ext.UserID)
	if user == nil {
		logx.Errorf("调试接口：用户不存在 RoomID:%v userID:%v", slf.RoomID, msg.Ext.UserID)
		return
	}

	// 检查地图是否存在
	if slf.mineMap == nil {
		logx.Errorf("调试接口：地图不存在 RoomID:%v userID:%v", slf.RoomID, user.UserID)
		user.SendMessage(msg.MsgID, ecode.ErrParams, "地图不存在")
		return
	}

	// 获取所有地雷位置
	mines := slf.getAllMinePositions()

	// 构建响应
	response := map[string]interface{}{
		"mines": mines,
	}

	// 发送响应
	user.SendMessage(msg.MsgID, ecode.OK, response)

	logx.Infof("调试接口：显示地雷位置 RoomID:%v userID:%v MineCount:%d",
		slf.RoomID, user.UserID, len(mines))
}

// getAllMinePositions 获取所有地雷位置（调试用）
// 注意：这是调试功能，仅用于开发测试
func (slf *Room) getAllMinePositions() []map[string]interface{} {
	if slf.mineMap == nil {
		return []map[string]interface{}{}
	}

	mines := make([]map[string]interface{}, 0)

	switch slf.mineMap.MapType {
	case constvar.MapTypeHexagon:
		// 六边形地图：遍历HexBlocks
		for _, hex := range slf.mineMap.ValidHexes {
			coordKey := fmt.Sprintf("%d,%d", hex.Q, hex.R)
			block := slf.mineMap.HexBlocks[coordKey]
			if block != nil && block.IsMine {
				mines = append(mines, map[string]interface{}{
					"q": hex.Q,
					"r": hex.R,
				})
			}
		}
	default:
		// 方格地图：遍历Blocks数组
		for y := 0; y < slf.mineMap.Height; y++ {
			for x := 0; x < slf.mineMap.Width; x++ {
				block := slf.mineMap.GetBlock(x, y)
				if block != nil && block.IsMine {
					mines = append(mines, map[string]interface{}{
						"x": x,
						"y": y,
					})
				}
			}
		}
	}

	return mines
}
