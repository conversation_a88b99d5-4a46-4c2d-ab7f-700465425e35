package roommgr

import (
	"fmt"
	"minesweep/common/logx"
	"minesweep/common/tools"

	"minesweep/constvar"
)

type (
	RollObstacle struct {
		ObstacleName string // 障碍物名称
		FromID       int    // 障碍物所在块ID
		ToID         int    // 最终ID
	}

	RollContext struct {
		OldChessPos int      // 旧的棋子位置
		DicePoint   int      // 单个骰子点数
		PropNames   []string // 本次掷骰子有效的道具名称列表(反转、倍数、盾牌、冰冻、前进)
	}
)

// Block 块
type Block struct {
	ID           int                   // 块的ID
	YOffset      int                   // y坐标偏移量
	Type         constvar.BlockType    // 块类型
	ObstacleType constvar.ObstacleType // 障碍物类型
	OtherID      int                   // 另一个块的ID
	Props        []constvar.GameProp   // 可选道具列表
}

// isInLastRow 是否是在最后一行 - 简化为扫雷游戏
func (b *Block) isInLastRow(mapType constvar.MapType) bool {
	// 扫雷游戏固定50格，最后一行是48-50
	if b.ID >= 48 {
		return true
	}
	return false
}

// isHaveProp 是否有某道具
func (b *Block) isHaveProp(propType constvar.GameProp) bool {
	for _, v := range b.Props {
		if v == propType {
			return true
		}
	}
	return false
}

// getSide 是否在地图的左边
func (b *Block) getSide() Side {
	pos := b.getPos()
	if b.ID <= 7 {
		if pos <= 3 {
			return LeftSide
		}
		return RightSide
	}

	if pos <= 4 {
		return LeftSide
	}
	return RightSide
}

// getRow 获取块的行坐标
func (b *Block) getRow() int {
	if b.ID <= 7 {
		return 1
	}

	return (b.ID-8)/8 + 2
}

// getColumn 获取块的列坐标(每行1-9列)
func (b *Block) getColumn() int {
	if b.ID <= 7 {
		return b.ID + 2
	}

	row := b.getRow()
	if row%2 == 1 {
		// 奇数行(2-9列)
		return (b.ID)%8 + 2
	}

	// 偶数行(1-8列)
	return 8 - (b.ID-8)%8
}

// getPos 获取块的位置(每行1-8个块)
func (b *Block) getPos() int {
	if b.ID <= 7 {
		return b.ID
	}

	row := b.getRow()
	if row%2 == 1 {
		// 奇数行(1-8)
		return (b.ID)%8 + 1
	}

	// 偶数行(1-8)
	return 8 - (b.ID-8)%8
}

// isGrass 该块是否需要长草
func (b *Block) isGrass() bool {
	if b.ID <= 7 {
		if b.ID >= 5 {
			return true
		}
		return false
	}

	row := b.getRow() // 行坐标
	pos := b.getPos() // 块的位置
	// 奇数行，5-8位置长草，偶数行，1~4位置长草
	if (row%2 == 1 && pos >= 5) || (row%2 == 0 && pos <= 4) {
		return true
	}
	return false
}

// getRowByID 获取id的行坐标
func (b *Block) getRowByID(id int) int {
	if id <= 7 {
		return 1
	}

	return (id-8)/8 + 2
}

// getColumnByID 获取id的列坐标(每行1-9列)
func (b *Block) getColumnByID(id int) int {
	if id <= 7 {
		return id + 2
	}

	row := b.getRowByID(id)
	if row%2 == 1 {
		// 奇数行(2-9列)
		return id%8 + 2
	}

	// 偶数行(1-8列)
	return 8 - (id-8)%8
}

// BlockMap 蛇梯棋游戏地图（已过时）
// 注意：此结构体仅为兼容性保留，扫雷游戏使用MineMap
// TODO: 在完全迁移到扫雷游戏后，应删除此结构体及相关代码
type BlockMap struct {
	roomID     int64
	mapType    constvar.MapType
	blocks     []*Block
	usedBlocks map[int]bool
}

func NewBlockMap(roomID int64, mapType constvar.MapType) *BlockMap {
	//TODO 初始化地图,这是原先蛇梯项目的初始化，后续需要根据地图类型生成扫雷地图
	blockMap := &BlockMap{roomID: roomID, mapType: mapType, usedBlocks: make(map[int]bool)}

	// 根据地图类型决定块数量，暂时所有类型都使用50个块
	var blockCount int
	switch mapType {
	case constvar.MapTypeGrid:
		blockCount = 50 // 方格地图50块
	case constvar.MapTypeHexagon:
		blockCount = 50 // 六边形地图也是50块
	default:
		blockCount = 50 // 默认50块
	}

	for i := 0; i < blockCount; i++ {
		blockMap.blocks = append(blockMap.blocks, &Block{ID: i + 1, Props: make([]constvar.GameProp, 0)})
	}
	return blockMap
}

// 为扫雷游戏添加简化的方法
// check 简化的地图检查方法
func (b *BlockMap) check() error {
	// 扫雷游戏的简化检查逻辑
	if len(b.blocks) != 50 {
		return fmt.Errorf("block count should be 50, got %d", len(b.blocks))
	}
	return nil
}

// print 简化的地图打印方法
func (b *BlockMap) print() {
	// 扫雷游戏的简化打印逻辑
	fmt.Printf("BlockMap: roomID=%d, mapType=%d, blocks=%d\n", b.roomID, b.mapType, len(b.blocks))
}

// getLadderPoints 简化的梯子点获取方法（扫雷游戏不需要）
func (b *BlockMap) getLadderPoints(isMultiple bool, isReverse bool, curPos int) []int {
	return []int{} // 扫雷游戏不需要梯子点
}

// getTrapPoints 简化的陷阱点获取方法（扫雷游戏不需要）
func (b *BlockMap) getTrapPoints(isMultiple bool, curPos int) []int {
	return []int{} // 扫雷游戏不需要陷阱点
}

func (b *BlockMap) getBlocks(obType constvar.ObstacleType) []*Block {
	var blocks = make([]*Block, 0)
	for _, v := range b.blocks {
		if v.ObstacleType == obType {
			blocks = append(blocks, v)
		}
	}
	return blocks
}

func (b *BlockMap) getBlock(id int) *Block {
	for _, v := range b.blocks {
		if v.ID == id {
			return v
		}
	}
	return nil
}

func (b *BlockMap) getMaxRow() int {
	switch b.mapType {
	case constvar.MapTypeHexagon:
		return 13 // 六边形地图行数更多
	default:
		return 7 // 方格地图默认7行
	}
}

// getRowPosCount 获取某行位置的数量
func (b *BlockMap) getRowPosCount(row int) int {
	if row <= 1 {
		return 7
	}

	switch b.mapType {
	case constvar.MapTypeHexagon:
		if row >= 13 {
			return 5
		}
		return 8
	default:
		if row >= 7 {
			return 3
		}
		return 8
	}
}

// getIDByPos 根据行坐标、pos获取ID
func (b *BlockMap) getIDByPos(row int, pos int) int {
	if row <= 1 {
		return pos
	}

	if row%2 == 1 {
		// 奇数行
		return 8*(row-3) + 15 + pos
	}

	// 偶数行
	return 8*(row-2) + 16 - pos
}

// getIDByColumn 根据行坐标、列坐标获取ID
func (b *BlockMap) getIDByColumn(row int, column int) int {
	if row <= 1 {
		return column - 2
	}

	if row%2 == 1 {
		// 奇数行
		return 8*(row-3) + 14 + column
	}

	// 偶数行
	return 8*(row-2) + 16 - column
}

// getColumnByPos 根据行位置获取column
func (b *BlockMap) getColumnByPos(row int, pos int) int {
	if row <= 1 {
		return pos + 2
	}

	if row%2 == 1 {
		// 奇数行
		return pos + 1
	}

	// 偶数行
	return pos
}

// getPosByColumn 根据column获取行位置
func (b *BlockMap) getPosByColumn(row int, column int) int {
	if row <= 1 {
		return column - 2
	}

	if row%2 == 1 {
		// 奇数行
		return column - 1
	}

	// 偶数行
	return column
}

// setUsedBlock 设置已使用的块
func (b *BlockMap) setUsedBlocks(ids []int) {
	for _, id := range ids {
		b.usedBlocks[id] = true
	}
}

// isBlockUsed 块是否已被使用
func (b *BlockMap) isBlockUsed(id int) bool {
	_, ok := b.usedBlocks[id]
	return ok
}

// isNoneBlock row+column的地方是否为空
func (b *BlockMap) isNoneBlock(row int, column int) bool {
	if row <= 1 {
		return column <= 2
	}

	if row%2 == 1 {
		// 奇数行
		return column == 1
	}

	// 偶数行
	return column == 9
}

// getObstacleCount 获取某行障碍物的个数(强化块除外)
func (b *BlockMap) getObstacleCount(row int) int {
	var count int
	var posCount = b.getRowPosCount(row)
	for pos := 1; pos <= posCount; pos++ {
		id := b.getIDByPos(row, pos)
		block := b.getBlock(id)
		if block != nil && block.ObstacleType > 0 && block.ObstacleType != constvar.ObstacleTypePowerUp {
			count++
		}
	}
	return count
}

// getRowUsedPosList 获取某行被使用过的位置列表
func (b *BlockMap) getRowUsedPosList(row int) map[int]bool {
	var posCount = b.getRowPosCount(row)
	var usedMap = make(map[int]bool)
	for pos := 1; pos <= posCount; pos++ {
		id := b.getIDByPos(row, pos)
		if b.isBlockUsed(id) {
			usedMap[pos] = true
		}
	}
	return usedMap
}

// getBouncerPos 获取某行弹跳器的位置
func (b *BlockMap) getBouncerPos(row int) int {
	if row <= 1 {
		return 7
	}

	if row%2 == 1 {
		// 奇数行
		return 8
	}
	return 1
}

// lotteryRow 从大于lastRow的行列表中随机一个
func (b *BlockMap) lotteryRow(lastRow int, rowWeights constvar.IntSlice) int {
	if lastRow <= 0 {
		return tools.LotteryDraw(rowWeights) + 1
	}

	var intSlice = make(constvar.IntSlice, len(rowWeights))
	copy(intSlice, rowWeights)
	for i, _ := range intSlice {
		var row = i + 1
		if row <= lastRow {
			intSlice[i] = 0
			continue
		}
	}
	if intSlice.Empty() {
		return -1
	}
	return tools.LotteryDraw(intSlice) + 1
}

// lotteryBouncerRow 从大于lastRow的行列表中随机一个
func (b *BlockMap) lotteryBouncerRow(lastRow int, rowWeights constvar.IntSlice) int {
	if lastRow <= 0 {
		return tools.LotteryDraw(rowWeights) + 1
	}

	var intSlice = make(constvar.IntSlice, len(rowWeights))
	copy(intSlice, rowWeights)
	for i, _ := range intSlice {
		var row = i + 1
		if row <= lastRow {
			intSlice[i] = 0
		}

		// 弹跳器的后一个块不能放障碍物
		var pos = b.getBouncerPos(row)
		var id = b.getIDByPos(row, pos)
		if b.isBlockUsed(id + 1) {
			intSlice[i] = 0
		}
	}
	if intSlice.Empty() {
		return -1
	}
	return tools.LotteryDraw(intSlice) + 1
}

// lotteryPos 从未被使用过的位置列表中随机一个
func (b *BlockMap) lotteryPos(row int, posWeights constvar.IntSlice) int {
	var intSlice = make(constvar.IntSlice, len(posWeights))
	copy(intSlice, posWeights)

	usedPosMap := b.getRowUsedPosList(row)
	for i, _ := range intSlice {
		var pos = i + 1
		// 位置已被使用，权重置为0
		if _, ok := usedPosMap[pos]; ok {
			intSlice[i] = 0
		}
	}
	if intSlice.Empty() {
		return -1
	}
	return tools.LotteryDraw(intSlice) + 1
}

// lotterySpikesPos 随机尖刺的位置
func (b *BlockMap) lotterySpikesPos(row int, posWeights constvar.IntSlice) int {
	var intSlice = make(constvar.IntSlice, len(posWeights))
	copy(intSlice, posWeights)

	// 前进方向，遇到尖刺倒退一步后，不能是梯子底部
	var bottomIDMap = make(map[int]bool)
	var blocks = b.getBlocks(constvar.ObstacleTypeLadder)
	for _, v := range blocks {
		bottomIDMap[v.ID] = true
	}

	// 尖刺都不在同一列
	var oldColumns = make(map[int]bool)
	spikesBlocks := b.getBlocks(constvar.ObstacleTypeSpikes)
	for _, v := range spikesBlocks {
		oldColumns[v.getColumn()] = true
	}

	usedPosMap := b.getRowUsedPosList(row)
	for i, _ := range intSlice {
		var pos = i + 1
		// 位置已被使用，权重置为0
		if _, ok := usedPosMap[pos]; ok {
			intSlice[i] = 0
			continue
		}

		// 如果8号块有梯子，则7号块不能有地刺(后退停到7号地刺上，无法处理)
		if row == 1 && pos == 7 {
			block8 := b.getBlock(8)
			if block8 != nil && block8.ObstacleType == constvar.ObstacleTypeLadder {
				intSlice[i] = 0
				continue
			}
		}

		// 尖刺都不在同一列
		var column = b.getColumnByPos(row, pos)
		if oldColumns[column] {
			intSlice[i] = 0
			continue
		}

		// 尖刺后退一步的块，不能是梯子底部
		var id = b.getIDByPos(row, pos)
		var backID = id - 1
		if _, ok := bottomIDMap[backID]; ok {
			intSlice[i] = 0
			continue
		}
	}
	if intSlice.Empty() {
		return -1
	}
	return tools.LotteryDraw(intSlice) + 1
}

// lotteryPowerUpPos 随机强化块的位置
func (b *BlockMap) lotteryPowerUpPos(row int, posWeights constvar.IntSlice) int {
	var intSlice = make(constvar.IntSlice, len(posWeights))
	copy(intSlice, posWeights)

	var lastID int     // 最近的一个强化块
	var lastColumn int // 最近一个强化块所在列
	var blocks = b.getBlocks(constvar.ObstacleTypePowerUp)
	if len(blocks) > 0 {
		lastID = blocks[len(blocks)-1].ID
		lastColumn = blocks[len(blocks)-1].getColumn()
	}

	// 列强化块数量限制(不能某一列强化块太多了)
	var columnCounts = make(map[int]int)
	for _, v := range blocks {
		columnCounts[v.getColumn()] += 1
	}

	// 设置单列最大强化块数量
	var maxColumnCount int
	if b.mapType == constvar.MapTypeHexagon {
		maxColumnCount = 3
	} else {
		maxColumnCount = 2
	}

	usedPosMap := b.getRowUsedPosList(row)
	for i, _ := range intSlice {
		var pos = i + 1
		// 位置已被使用，权重置为0
		if _, ok := usedPosMap[pos]; ok {
			intSlice[i] = 0
			continue
		}

		// 两个强化块之间的间隔最小为3
		var id = b.getIDByPos(row, pos)
		if lastID > 0 && id-lastID <= 3 {
			intSlice[i] = 0
			continue
		}

		// 单列最大强化块数量限制
		var column = b.getColumnByPos(row, pos)
		var columnCount = columnCounts[column]
		if columnCount+1 >= maxColumnCount {
			intSlice[i] = 0
			continue
		}

		// 减少相邻强化块在相同列的概率，额外减少40%
		if column == lastColumn && tools.RandNum(100) < 40 {
			intSlice[i] = 0
			continue
		}
	}

	// 不能某一行没有强化块
	if intSlice.Empty() {
		var posCount = b.getRowPosCount(row)
		var emptyPosList []int
		for pos := 1; pos <= posCount; pos++ {
			if _, ok := usedPosMap[pos]; !ok {
				emptyPosList = append(emptyPosList, pos)
			}
		}
		if len(emptyPosList) > 0 {
			return emptyPosList[tools.RandNum(len(emptyPosList))]
		}
		return -1
	}
	return tools.LotteryDraw(intSlice) + 1
}

// lotterySnakeHeadPos 随机蛇头的位置
func (b *BlockMap) lotterySnakeHeadPos(row int, posWeights constvar.IntSlice, height int, isLast bool) int {
	var intSlice = make(constvar.IntSlice, len(posWeights))
	copy(intSlice, posWeights)

	// 单列最大蛇头数量统计
	var blocks = b.getBlocks(constvar.ObstacleTypeSnake)
	var columnCounts = make(map[int]int)
	for _, v := range blocks {
		columnCounts[v.getColumn()] += 1
	}
	// 设置单列最大蛇头数量 - 简化为扫雷游戏
	var maxColumnCount int = 2 // 扫雷游戏固定值

	// 防止所有蛇头都在地图某一侧
	var lastSide Side
	if len(blocks) > 0 {
		lastSide = blocks[len(blocks)-1].getSide()
	}

	// 高度为1时, 蛇头不出现在两侧
	if height == 1 {
		for i, _ := range intSlice {
			var pos = i + 1
			// 奇数行，蛇头不出现在最左侧
			if row%2 == 1 && pos == 1 {
				intSlice[i] = 0
				continue
			}
			// 偶数行，蛇头不出现在最右侧
			if row%2 == 0 && pos == 8 {
				intSlice[i] = 0
				continue
			}

			// 奇数行，蛇头在右边的权重增加
			if row%2 == 1 && pos >= 5 && intSlice[i] > 0 {
				intSlice[i] += 5
				continue
			}

			// 偶数行，蛇头在左边的权重增加
			if row%2 == 0 && pos <= 4 && intSlice[i] > 0 {
				intSlice[i] += 5
				continue
			}
		}
	}

	// 简化为扫雷游戏 - 不需要复杂的侧边限制逻辑
	var isSideLimit bool = false

	// 单列最大块数量限制
	for i, _ := range intSlice {
		var pos = i + 1
		var column = b.getColumnByPos(row, pos)

		// 单列最大蛇头数量限制
		if !isLast && columnCounts[column] >= maxColumnCount {
			intSlice[i] = 0
			continue
		}

		// 设置一定概率，相邻蛇不在相同的边(最后一条蛇除外)
		if !isLast && lastSide > 0 && isSideLimit {
			if lastSide == LeftSide && pos <= 4 {
				intSlice[i] = 0
				continue
			}
			if lastSide == RightSide && pos >= 5 {
				intSlice[i] = 0
				continue
			}
		}
	}

	usedPosMap := b.getRowUsedPosList(row)
	for i, _ := range intSlice {
		var pos = i + 1
		// 位置已被使用，权重置为0
		if _, ok := usedPosMap[pos]; ok {
			intSlice[i] = 0
			continue
		}
	}
	if intSlice.Empty() {
		return -1
	}
	return tools.LotteryDraw(intSlice) + 1
}

// lotterySnakeTailPos 随机蛇尾的位置
func (b *BlockMap) lotterySnakeTailPos(tailRow int, headPos int, widthWeights constvar.IntSlice, height int) int {
	var intSlice = make(constvar.IntSlice, len(widthWeights))
	copy(intSlice, widthWeights)

	var oldColumns = make(map[string]bool)               // 已放置蛇的占用列
	var oldTailsPos = make(map[int]bool)                 // 已放置蛇的蛇尾位置(两条蛇尾在同一行不能相邻位置)
	var blocks = b.getBlocks(constvar.ObstacleTypeSnake) // 已放置蛇列表
	for _, v := range blocks {
		tailBlock := b.getBlock(v.OtherID)
		if tailBlock == nil {
			continue
		}
		tailColumn := tailBlock.getColumn()

		// tailRow上的蛇尾统计
		if tailBlock.getRow() == tailRow {
			oldTailsPos[tailBlock.getPos()] = true
		}

		// 标记该条蛇，占用了蛇尾到蛇头的哪些列
		for row := tailBlock.getRow(); row < v.getRow(); row++ {
			oldColumns[fmt.Sprintf("%v_%v", row, tailColumn)] = true

			// 已占用列的左右两边也不能重合
			var leftColumn = tailColumn - 1
			var rightColumn = tailColumn + 1
			if leftColumn >= 1 && leftColumn <= 9 {
				oldColumns[fmt.Sprintf("%v_%v", row, leftColumn)] = true
			}
			if rightColumn >= 1 && rightColumn <= 9 {
				oldColumns[fmt.Sprintf("%v_%v", row, rightColumn)] = true
			}
		}
	}

	// 简化为扫雷游戏 - 不需要复杂的宽度限制
	// var maxWidth int = 0 // 扫雷游戏不需要宽度限制
	/*
		switch b.mapType {
		case constvar.MapTypeHexagon:
			// 100格地图
			// 比较高的蛇不能太宽(第一条蛇除外)，否则与低层的蛇频繁交叉
			switch height {
			case 4:
				maxWidth = 2
			case 3:
				maxWidth = 3
			case 2:
				maxWidth = 4
			}
		default:
			// 50格地图
			// 比较高的蛇不能太宽(第一条蛇除外)，否则与低层的蛇频繁交叉
			switch height {
			case 3:
				maxWidth = 2
			case 2:
				maxWidth = 3
			}
		}

		var posCount = b.getRowPosCount(tailRow)      // 获取某行位置的数量
		var usedPosMap = b.getRowUsedPosList(tailRow) // 获取某行已被使用的位置列表
		var tailPosSlice = make(constvar.IntSlice, posCount)
		for width, weight := range intSlice {
			if weight <= 0 || (maxWidth > 0 && width > maxWidth) {
				continue
			}
			var headID = b.getIDByPos(tailRow+height, headPos) // 蛇头ID

			var leftTailPos = b.getPosByColumn(tailRow, b.getColumnByPos(tailRow+height, headPos-width)) // 宽度左边的位置
			var leftTailID = b.getIDByPos(tailRow, leftTailPos)                                          // 宽度左边的位置的块ID
			var leftColumn = b.getColumnByPos(tailRow, leftTailPos)                                      // 宽度左边的列
			if leftTailPos >= 1 && leftTailPos <= posCount && !usedPosMap[leftTailPos] && headID-leftTailID >= 6 {
				// leftPos合法，并且未被使用过，并且蛇头id、蛇尾id间隔不能太小
				if !oldTailsPos[leftTailPos-1] && !oldTailsPos[leftTailPos+1] { // 蛇尾在同一行不能相邻位置
					// 判断该蛇尾到蛇头所在列，是否已被其它蛇使用
					var isColumnUsed bool
					for row := tailRow; row < tailRow+height; row++ {
						field := fmt.Sprintf("%v_%v", row, leftColumn)
						if _, ok := oldColumns[field]; ok {
							isColumnUsed = true
							break
						}
					}
					if !isColumnUsed {
						tailPosSlice[leftTailPos-1] = weight
					}
				}
			}

			var rightTailPos = b.getPosByColumn(tailRow, b.getColumnByPos(tailRow+height, headPos+width)) // 宽度右边的位置
			var rightTailID = b.getIDByPos(tailRow, rightTailPos)                                         // 宽度右边的位置的块ID
			var rightColumn = b.getColumnByPos(tailRow, rightTailPos)                                     // 宽度右边的列
			if rightTailPos >= 1 && rightTailPos <= posCount && !usedPosMap[rightTailPos] && headID-rightTailID >= 6 {
				// rightPos合法，并且未被使用过，并且蛇头id、蛇尾id间隔不能太小
				if !oldTailsPos[rightTailPos-1] && !oldTailsPos[rightTailPos+1] { // 蛇尾在同一行不能相邻位置
					// 判断该蛇尾到蛇头所在列，是否已被其它蛇使用
					var isColumnUsed bool
					for row := tailRow; row < tailRow+height; row++ {
						field := fmt.Sprintf("%v_%v", row, rightColumn)
						if _, ok := oldColumns[field]; ok {
							isColumnUsed = true
							break
						}
					}
					if !isColumnUsed {
						tailPosSlice[rightTailPos-1] = weight
					}
				}
			}
		}
		if tailPosSlice.Empty() {
			return -1
		}
		return tools.LotteryDraw(tailPosSlice) + 1
	*/
	// 扫雷游戏简化版本
	return 1
}

// lotteryTrapBoxPos 从未被使用过的位置列表中随机一个
func (b *BlockMap) lotteryTrapBoxPos(row int, posWeights constvar.IntSlice) int {
	var intSlice = make(constvar.IntSlice, len(posWeights))
	copy(intSlice, posWeights)

	var usedPosMap = b.getRowUsedPosList(row)
	var posList []int
	for i, _ := range intSlice {
		var pos = i + 1
		// 位置已被使用，权重置为0
		if _, ok := usedPosMap[pos]; ok {
			intSlice[i] = 0
			continue
		}

		// 陷阱箱的下一行，必须有块，并且该块不能有障碍物
		var column = b.getColumnByPos(row, pos)
		var bottomID = b.getIDByColumn(row-1, column)
		var block = b.getBlock(bottomID)
		if block == nil || (block.getRow() != row-1 || block.getColumn() != column || b.isBlockUsed(bottomID)) {
			intSlice[i] = 0
			continue
		}
		posList = append(posList, pos)

		// 陷阱箱顶部id和底部id不能间隔太近
		var id = b.getIDByPos(row, pos)
		if id-bottomID <= 4 {
			intSlice[i] = 0
			continue
		}
	}
	if intSlice.Empty() {
		// 陷阱箱必须得有
		if len(posList) > 0 {
			return posList[tools.RandNum(len(posList))]
		}
		return -1
	}
	return tools.LotteryDraw(intSlice) + 1
}

// lotteryHeightByLadder 梯子高度随机
func (b *BlockMap) lotteryHeightByLadder(row int, heightWeights constvar.IntSlice, maxHeightSum int, isLast bool) int {
	var intSlice = make(constvar.IntSlice, len(heightWeights))
	copy(intSlice, heightWeights)

	var oldHeightSum int                // 已放置梯子的高度之和
	var lastHeights = make(map[int]int) // 最近一条梯子的高度-->数量
	var oldHeights = make(map[int]int)  // 已放置梯子的高度-->数量
	var oldTopRows = make(map[int]int)  // 已放置梯子的顶部所在行-->数量
	var oldColumns = make(map[int]int)  // 有梯子穿过的行-->数量
	blocks := b.getBlocks(constvar.ObstacleTypeLadder)
	for _, v := range blocks {
		topBlock := b.getBlock(v.OtherID)
		if topBlock == nil {
			continue
		}
		bRow := v.getRow()        // 梯子底部所在行
		tRow := topBlock.getRow() // 梯子顶部所在行

		height := tRow - bRow // 梯子高度
		if len(lastHeights) == 0 {
			lastHeights[height] += 1
		} else {
			if lastHeights[height] > 0 {
				lastHeights[height] += 1
			}
		}

		oldHeights[height] += 1
		oldTopRows[tRow] += 1
		oldHeightSum += height
		for i := bRow; i <= tRow; i++ {
			oldColumns[i] += 1
		}
	}

	// 简化为扫雷游戏 - 不需要复杂的高度统计
	var heightCountStat = map[int]int{1: 2, 2: 3, 3: 1, 4: 0} // 固定值
	var maxHeight int = b.getMaxRow() - row - 1               // 扫雷游戏固定计算

	for i, _ := range intSlice {
		var height = i + 1
		if height > maxHeight {
			intSlice[i] = 0
			continue
		}

		// 第一行的梯子，高度必须大于1
		if row == 1 && height == 1 {
			intSlice[i] = 0
			continue
		}

		// 已放置梯子的高度已超过限制数量
		var heightCount = oldHeights[height]
		if !isLast && heightCount >= heightCountStat[height] {
			intSlice[i] = 0
			continue
		}

		// 不能出现连续3条相同高度的梯子
		if len(lastHeights) >= 2 && lastHeights[height] > 0 {
			intSlice[i] = 0
			continue
		}

		// 梯子顶端必须合法，每行的梯子顶端数量不能超过2个
		var topRow = row + height
		var topCount = oldTopRows[topRow]
		if topRow <= 0 || topCount >= 2 {
			intSlice[i] = 0
			continue
		}

		// 该高度，是否会导致，有超过2个梯子并行
		for r := row; r <= topRow; r++ {
			if oldColumns[r] >= 2 {
				intSlice[i] = 0
			}
		}

		// 梯子的高度之和，已经超标，返回梯子的最小高度1
		if height+oldHeightSum > maxHeightSum {
			if height > 1 {
				intSlice[i] = 0
				continue
			}
			return 1
		}

		// 简化为扫雷游戏 - 不需要特殊的高度限制
		// 扫雷游戏不需要这种复杂的限制逻辑
	}
	if intSlice.Empty() {
		logx.Infof("RoomID:%v lotteryHeightByLadder oldHeights:%v, maxHeight:%v, oldHeightSum:%v", b.roomID, oldHeights, maxHeight, oldHeightSum)
		return -1
	}

	return tools.LotteryDraw(intSlice) + 1
}

// lotteryPosByLadder 从未被使用过的位置列表重随机一个
func (b *BlockMap) lotteryPosByLadder(row int, topRow int, posWeights constvar.IntSlice) int {
	var intSlice = make(constvar.IntSlice, len(posWeights))
	copy(intSlice, posWeights)

	var oldColumns = make(map[string]bool)
	blocks := b.getBlocks(constvar.ObstacleTypeLadder)
	for _, v := range blocks {
		tBlock := b.getBlock(v.OtherID)
		if tBlock == nil {
			continue
		}

		bRow := v.getRow()      // 梯子底部所在行
		tRow := tBlock.getRow() // 梯子顶部所在行
		for i := bRow; i <= tRow; i++ {
			field := fmt.Sprintf("%v_%v", i, v.getColumn())
			oldColumns[field] = true
		}
	}

	var lastColumn int // 最近一个梯子所在列
	if len(blocks) > 0 {
		lastColumn = blocks[len(blocks)-1].getColumn()
	}

	var usedPosMap = b.getRowUsedPosList(row)       // row行中已被使用的位置列表
	var usedTopPosMap = b.getRowUsedPosList(topRow) // topRow行中已被使用的位置列表

	for i, _ := range intSlice {
		var pos = i + 1
		// 位置已被使用，权重置为0
		if _, ok := usedPosMap[pos]; ok {
			intSlice[i] = 0
			continue
		}

		// top位置已被使用，权重置为0
		topPos := b.getPosByColumn(topRow, b.getColumnByPos(row, pos))
		if _, ok := usedTopPosMap[topPos]; ok {
			intSlice[i] = 0
			continue
		}

		// 梯子顶部的位置是否为空(topRow, column)位置
		if b.isNoneBlock(topRow, b.getColumnByPos(row, pos)) {
			intSlice[i] = 0
			continue
		}

		// 简化为扫雷游戏 - 使用固定的块数量限制
		var topID = b.getIDByPos(topRow, topPos)
		var bottomID = b.getIDByPos(row, pos)
		if topID-bottomID <= 5 || topID >= 50 { // 扫雷游戏固定50格
			intSlice[i] = 0
			continue
		}

		// 旧梯子是否已使用
		for k := row; k <= topRow; k++ {
			var column = b.getColumnByPos(k, pos)
			if _, ok := oldColumns[fmt.Sprintf("%v_%v", k, column)]; ok {
				intSlice[i] = 0
				break
			}
		}

		// 相邻的两个梯子，不在相同列和相邻列
		var column = b.getColumnByPos(row, pos)
		if column == lastColumn ||
			column == lastColumn-1 ||
			column == lastColumn+1 {
			intSlice[i] = 0
			continue
		}
	}
	if intSlice.Empty() {
		return -1
	}
	return tools.LotteryDraw(intSlice) + 1
}

// lotteryProp 从未被使用过的位置列表中按权重随机一个
func (b *BlockMap) lotteryProp(propWeights constvar.IntSlice, propCountMap map[constvar.GameProp]int, diffMap map[constvar.GameProp]bool, playerNum int) constvar.GameProp {
	var intSlice = make(constvar.IntSlice, len(propWeights))
	copy(intSlice, propWeights)

	// 简化为扫雷游戏 - 固定道具数量限制
	var propCountLimits = map[constvar.GameProp]int{1: 2, 2: 2, 3: 2, 4: 2, 5: 2, 6: 2, 7: 2}
	// 2个或者3个玩家的地图(不管50格还是100格)，没有Switch道具
	if playerNum == 2 || playerNum == 3 {
		// 无Switch时，6*6 >= 3*11
		propCountLimits[constvar.GamePropSwitch] = 0
		for i := 2; i <= 7; i++ {
			propCountLimits[constvar.GameProp(i)] += 1
		}
	} else {
		// 有Switch时，5*6 + 4 >= 3*11
		propCountLimits[constvar.GamePropSwitch] = 4
	}

	for i, _ := range intSlice {
		// 每种道具数量限制
		var propType = constvar.GameProp(i + 1)
		if propCountMap[propType] >= propCountLimits[propType] {
			intSlice[i] = 0
			continue
		}

		// 单个强化块上，道具不能重复出现
		if _, ok := diffMap[propType]; ok {
			intSlice[i] = 0
			continue
		}
	}
	if intSlice.Empty() {
		// 确保每个强化块上必须有3种道具
		var propList []constvar.GameProp
		for i := 2; i <= 7; i++ {
			var propType = constvar.GameProp(i)
			// 单个强化块上，道具不能重复出现
			if _, ok := diffMap[propType]; ok {
				continue
			}
			propList = append(propList, propType)
		}
		if len(propList) > 0 {
			return propList[tools.RandNum(len(propList))]
		}
		return -1
	}

	// 数量-->道具类型列表
	var propStat = make(map[int][]constvar.GameProp)
	for propType, count := range propCountMap {
		// 单个强化块上，道具不能重复出现
		if _, ok := diffMap[propType]; ok {
			continue
		}
		// Switch道具不参与，因为Switch道具放的比较少
		if propType == constvar.GamePropSwitch {
			continue
		}
		propStat[count] = append(propStat[count], propType)
	}

	// 随机出来的道具次数已经超标
	propType := constvar.GameProp(tools.LotteryDraw(intSlice) + 1)
	propCount := propCountMap[propType]
	switch propCount {
	case 5:
		// 只有100格地图，遍历0-3格数量的道具随机
		for i := 0; i <= 3; i++ {
			if len(propStat[i]) <= 0 {
				continue
			}
			propType = propStat[i][tools.RandNum(len(propStat[i]))]
		}
	case 4:
		// 只有100格地图，遍历0-2格数量的道具随机
		for i := 0; i <= 2; i++ {
			if len(propStat[i]) <= 0 {
				continue
			}
			propType = propStat[i][tools.RandNum(len(propStat[i]))]
		}
	case 3:
		// 只有100格地图，遍历0-1格数量的道具随机
		for i := 0; i <= 1; i++ {
			if len(propStat[i]) <= 0 {
				continue
			}
			propType = propStat[i][tools.RandNum(len(propStat[i]))]
		}
	case 2:
		// 50、100格地图，遍历0数量的道具随机
		if len(propStat[0]) > 0 {
			propType = propStat[0][tools.RandNum(len(propStat[0]))]
		}
	}
	propCountMap[propType]++
	diffMap[propType] = true

	return propType
}

// lotteryHeightBySnake 蛇高度随机
func (b *BlockMap) lotteryHeightBySnake(row int, heightWeights constvar.IntSlice, maxHeightSum int) int {
	var intSlice = make(constvar.IntSlice, len(heightWeights))
	copy(intSlice, heightWeights)

	var oldHeightSum int                // 已放置蛇的高度之和
	var isLastHeightsStop bool          // lastHeights的统计是否中断
	var lastHeights = make(map[int]int) // 最近一条蛇的高度-->数量
	var oldHeights = make(map[int]int)  // 已放置蛇的高度-->数量
	var oldTailRows = make(map[int]int) // 已放置蛇的蛇尾所在行-->数量
	blocks := b.getBlocks(constvar.ObstacleTypeSnake)
	for i := len(blocks) - 1; i >= 0; i-- {
		topBlock := blocks[i]                     // 蛇头
		tailBlock := b.getBlock(topBlock.OtherID) // 蛇尾
		if tailBlock == nil {
			continue
		}

		var height = topBlock.getRow() - tailBlock.getRow() // 蛇高度
		if !isLastHeightsStop {
			if len(lastHeights) == 0 {
				lastHeights[height] = 1
			} else {
				if lastHeights[height] > 0 {
					// height连续，继续统计
					lastHeights[height] += 1
				} else {
					// 如果height中断了，停止统计
					isLastHeightsStop = true
				}
			}
		}

		oldHeights[height] += 1
		oldTailRows[tailBlock.getRow()] += 1
		oldHeightSum += height
	}

	// 简化为扫雷游戏 - 固定高度统计
	var heightCountStat = map[int]int{1: 2, 2: 3, 3: 1, 4: 0}

	for i, _ := range intSlice {
		var height = i + 1
		var heightCount = oldHeights[height]

		// 已放置蛇的高度已超过限制数量
		if heightCount >= heightCountStat[height] {
			intSlice[i] = 0
			continue
		}

		// 简化为扫雷游戏 - 不需要这种限制
		// 扫雷游戏不需要连续高度限制

		// 蛇尾必须合法，每行的蛇尾数量不能超过2条
		var tailRow = row - height
		var tailCount = oldTailRows[tailRow]
		if tailRow <= 0 || tailCount >= 2 {
			intSlice[i] = 0
			continue
		}

		// 蛇尾直接在第一行(这种情况的权重需要减少)
		if tailRow == 1 && intSlice[i] > 0 {
			intSlice[i] = 20
			continue
		}

		// 蛇的高度之和，已经超标，返回蛇的最小高度1
		if height+oldHeightSum > maxHeightSum {
			if height > 1 {
				intSlice[i] = 0
				continue
			}
			return 1
		}
	}
	if intSlice.Empty() {
		return -1
	}
	return tools.LotteryDraw(intSlice) + 1
}

// lotterySpikesRow 从大于lastRow的行列表中随机一个
func (b *BlockMap) lotterySpikesRow(lastRow int, rowWeights constvar.IntSlice) int {
	if lastRow <= 0 {
		return tools.LotteryDraw(rowWeights) + 1
	}

	var intSlice = make(constvar.IntSlice, len(rowWeights))
	copy(intSlice, rowWeights)

	for i, _ := range intSlice {
		var row = i + 1
		if row <= lastRow {
			intSlice[i] = 0
			continue
		}

	}
	if intSlice.Empty() {
		return -1
	}

	// 简化为扫雷游戏 - 不需要特殊的行选择逻辑
	// 扫雷游戏使用标准的随机选择
	return tools.LotteryDraw(intSlice) + 1
}

// lotteryLadderRow 从大于lastRow的行列表中随机一个
func (b *BlockMap) lotteryLadderRow(lastRow int, rowWeights constvar.IntSlice) int {
	if lastRow <= 0 {
		return tools.LotteryDraw(rowWeights) + 1
	}

	var oldRows = make(map[int]int)
	blocks := b.getBlocks(constvar.ObstacleTypeLadder)
	for _, v := range blocks {
		tBlock := b.getBlock(v.OtherID)
		if tBlock == nil {
			continue
		}

		bRow := v.getRow()      // 梯子底部所在行
		tRow := tBlock.getRow() // 梯子顶部所在行
		for i := bRow; i <= tRow; i++ {
			oldRows[i] += 1
		}
	}

	var intSlice = make(constvar.IntSlice, len(rowWeights))
	copy(intSlice, rowWeights)
	for i, _ := range intSlice {
		var row = i + 1
		if row <= lastRow {
			intSlice[i] = 0
			continue
		}

		// 本行已经有梯子穿过数量不能超过2个
		if oldRows[row] >= 2 {
			intSlice[i] = 0
			continue
		}
	}
	if intSlice.Empty() {
		return -1
	}
	return tools.LotteryDraw(intSlice) + 1
}

// isSetSevenLadder 是否设置第七个梯子 - 简化为扫雷游戏
func (b *BlockMap) isSetSevenLadder() bool {
	// 扫雷游戏不需要第七个梯子
	return false

	blocks := b.getBlocks(constvar.ObstacleTypeLadder)
	if len(blocks) != 6 {
		return false
	}

	topBlock := b.getBlock(blocks[5].OtherID)
	if topBlock == nil {
		return false
	}

	if topBlock.getRow() >= b.getMaxRow()-1 {
		return false
	}
	return true
}

// 删除重复的 print 方法定义，使用前面的简化版本

// check 检查地图合法性 - 简化版本（删除重复定义）
// 这个方法已经在前面定义过了，删除重复定义

// 删除重复的方法定义，使用前面的简化版本

// 删除重复的方法定义，使用前面的简化版本
