package roommgr

import (
	"fmt"
	"minesweep/common/logx"
)

// 单机关卡系统专用的六边形地图配置
// 这些配置用于特殊关卡（第5、10、15、20、25、30关）

// 使用map结构以适配HTML工具生成的格式
var levelHexMapConfigs = map[int]*HexMapConfig{
	// HexMapID: 0 - 第5关专用（4颗地雷）
	0: {
		MapID:   0,
		MapName: "01",
		ValidHexes: []HexCoord{
			{Q: 0, R: -1},
			{Q: 0, R: 0},
			{Q: 1, R: -2},
			{Q: 1, R: -1},
			{Q: 1, R: 0},
			{Q: 2, R: -3},
			{Q: 2, R: -2},
			{Q: 2, R: -1},
			{Q: 2, R: 0},
			{Q: 3, R: -3},
			{Q: 3, R: -2},
			{Q: 3, R: -1},
			{Q: 3, R: 0},
			{Q: 4, R: -4},
			{Q: 4, R: -3},
			{Q: 4, R: -2},
			{Q: 4, R: -1},
			{Q: 5, R: -4},
			{Q: 5, R: -3},
			{Q: 5, R: -2},
		},
		MineCount: 4,
		NeighborMap: map[string][]HexCoord{
			"1,-1": {{Q: 2, R: -1}, {Q: 2, R: -2}, {Q: 1, R: -2}, {Q: 0, R: -1}, {Q: 1, R: 0}},
			"1,0":  {{Q: 2, R: 0}, {Q: 2, R: -1}, {Q: 1, R: -1}},
			"0,-1": {{Q: 1, R: -1}, {Q: 1, R: -2}},
			"1,-2": {{Q: 2, R: -2}, {Q: 2, R: -3}, {Q: 0, R: -1}, {Q: 1, R: -1}},
			"2,-3": {{Q: 3, R: -3}, {Q: 1, R: -2}, {Q: 2, R: -2}},
			"2,-2": {{Q: 3, R: -2}, {Q: 3, R: -3}, {Q: 2, R: -3}, {Q: 1, R: -2}, {Q: 1, R: -1}, {Q: 2, R: -1}},
			"2,-1": {{Q: 3, R: -1}, {Q: 3, R: -2}, {Q: 2, R: -2}, {Q: 1, R: -1}, {Q: 1, R: 0}, {Q: 2, R: 0}},
			"2,0":  {{Q: 3, R: 0}, {Q: 3, R: -1}, {Q: 2, R: -1}, {Q: 1, R: 0}},
			"3,-3": {{Q: 4, R: -3}, {Q: 4, R: -4}, {Q: 2, R: -3}, {Q: 2, R: -2}, {Q: 3, R: -2}},
			"3,-2": {{Q: 4, R: -2}, {Q: 4, R: -3}, {Q: 3, R: -3}, {Q: 2, R: -2}, {Q: 2, R: -1}, {Q: 3, R: -1}},
			"3,-1": {{Q: 4, R: -1}, {Q: 4, R: -2}, {Q: 3, R: -2}, {Q: 2, R: -1}, {Q: 2, R: 0}, {Q: 3, R: 0}},
			"3,0":  {{Q: 4, R: -1}, {Q: 3, R: -1}, {Q: 2, R: 0}},
			"4,-4": {{Q: 5, R: -4}, {Q: 3, R: -3}, {Q: 4, R: -3}},
			"4,-3": {{Q: 5, R: -3}, {Q: 5, R: -4}, {Q: 4, R: -4}, {Q: 3, R: -3}, {Q: 3, R: -2}, {Q: 4, R: -2}},
			"4,-2": {{Q: 5, R: -2}, {Q: 5, R: -3}, {Q: 4, R: -3}, {Q: 3, R: -2}, {Q: 3, R: -1}, {Q: 4, R: -1}},
			"4,-1": {{Q: 5, R: -2}, {Q: 4, R: -2}, {Q: 3, R: -1}, {Q: 3, R: 0}},
			"5,-4": {{Q: 4, R: -4}, {Q: 4, R: -3}, {Q: 5, R: -3}},
			"5,-3": {{Q: 5, R: -4}, {Q: 4, R: -3}, {Q: 4, R: -2}, {Q: 5, R: -2}},
			"5,-2": {{Q: 5, R: -3}, {Q: 4, R: -2}, {Q: 4, R: -1}},
		},
	},

	// HexMapID: 1 - 第10关专用（5颗地雷）
	1: {
		MapID:   1,
		MapName: "02",
		ValidHexes: []HexCoord{
			{Q: 0, R: -3},
			{Q: 0, R: -2},
			{Q: 0, R: -1},
			{Q: 0, R: 0},
			{Q: 1, R: -4},
			{Q: 1, R: -3},
			{Q: 1, R: -2},
			{Q: 1, R: -1},
			{Q: 1, R: 0},
			{Q: 2, R: -6},
			{Q: 2, R: -5},
			{Q: 2, R: -4},
			{Q: 2, R: -3},
			{Q: 2, R: -2},
			{Q: 2, R: -1},
			{Q: 3, R: -7},
			{Q: 3, R: -6},
			{Q: 3, R: -5},
			{Q: 3, R: -4},
			{Q: 4, R: -7},
			{Q: 4, R: -6},
			{Q: 4, R: -5},
		},
		MineCount: 5,
		NeighborMap: map[string][]HexCoord{
			"0,-1": {{Q: 1, R: -1}, {Q: 1, R: -2}, {Q: 0, R: -2}, {Q: 0, R: 0}},
			"0,0":  {{Q: 1, R: 0}, {Q: 1, R: -1}, {Q: 0, R: -1}},
			"0,-2": {{Q: 1, R: -2}, {Q: 1, R: -3}, {Q: 0, R: -3}, {Q: 0, R: -1}},
			"1,-3": {{Q: 2, R: -3}, {Q: 2, R: -4}, {Q: 1, R: -4}, {Q: 0, R: -3}, {Q: 0, R: -2}, {Q: 1, R: -2}},
			"1,-2": {{Q: 2, R: -2}, {Q: 2, R: -3}, {Q: 1, R: -3}, {Q: 0, R: -2}, {Q: 0, R: -1}, {Q: 1, R: -1}},
			"1,-1": {{Q: 2, R: -1}, {Q: 2, R: -2}, {Q: 1, R: -2}, {Q: 0, R: -1}, {Q: 0, R: 0}, {Q: 1, R: 0}},
			"2,-3": {{Q: 3, R: -4}, {Q: 2, R: -4}, {Q: 1, R: -3}, {Q: 1, R: -2}, {Q: 2, R: -2}},
			"2,-2": {{Q: 2, R: -3}, {Q: 1, R: -2}, {Q: 1, R: -1}, {Q: 2, R: -1}},
			"2,-1": {{Q: 2, R: -2}, {Q: 1, R: -1}, {Q: 1, R: 0}},
			"0,-3": {{Q: 1, R: -3}, {Q: 1, R: -4}, {Q: 0, R: -2}},
			"1,0":  {{Q: 2, R: -1}, {Q: 1, R: -1}, {Q: 0, R: 0}},
			"1,-4": {{Q: 2, R: -4}, {Q: 2, R: -5}, {Q: 0, R: -3}, {Q: 1, R: -3}},
			"2,-4": {{Q: 3, R: -4}, {Q: 3, R: -5}, {Q: 2, R: -5}, {Q: 1, R: -4}, {Q: 1, R: -3}, {Q: 2, R: -3}},
			"3,-4": {{Q: 4, R: -5}, {Q: 3, R: -5}, {Q: 2, R: -4}, {Q: 2, R: -3}},
			"2,-5": {{Q: 3, R: -5}, {Q: 3, R: -6}, {Q: 2, R: -6}, {Q: 1, R: -4}, {Q: 2, R: -4}},
			"3,-5": {{Q: 4, R: -5}, {Q: 4, R: -6}, {Q: 3, R: -6}, {Q: 2, R: -5}, {Q: 2, R: -4}, {Q: 3, R: -4}},
			"4,-5": {{Q: 4, R: -6}, {Q: 3, R: -5}, {Q: 3, R: -4}},
			"2,-6": {{Q: 3, R: -6}, {Q: 3, R: -7}, {Q: 2, R: -5}},
			"3,-6": {{Q: 4, R: -6}, {Q: 4, R: -7}, {Q: 3, R: -7}, {Q: 2, R: -6}, {Q: 2, R: -5}, {Q: 3, R: -5}},
			"4,-6": {{Q: 4, R: -7}, {Q: 3, R: -6}, {Q: 3, R: -5}, {Q: 4, R: -5}},
			"3,-7": {{Q: 4, R: -7}, {Q: 2, R: -6}, {Q: 3, R: -6}},
			"4,-7": {{Q: 3, R: -7}, {Q: 3, R: -6}, {Q: 4, R: -6}},
		},
	},

	// HexMapID: 2 - 第15关专用（10颗地雷）
	2: {
		MapID:   2,
		MapName: "03",
		ValidHexes: []HexCoord{
			{Q: 0, R: -1},
			{Q: 0, R: 0},
			{Q: 1, R: -5},
			{Q: 1, R: -4},
			{Q: 1, R: -2},
			{Q: 1, R: -1},
			{Q: 1, R: 0},
			{Q: 1, R: 1},
			{Q: 2, R: -6},
			{Q: 2, R: -5},
			{Q: 2, R: -4},
			{Q: 2, R: -3},
			{Q: 2, R: -2},
			{Q: 2, R: -1},
			{Q: 2, R: 0},
			{Q: 3, R: -7},
			{Q: 3, R: -6},
			{Q: 3, R: -5},
			{Q: 3, R: -4},
			{Q: 3, R: -3},
			{Q: 3, R: -2},
			{Q: 3, R: -1},
			{Q: 3, R: 0},
			{Q: 4, R: -7},
			{Q: 4, R: -6},
			{Q: 4, R: -5},
			{Q: 4, R: -4},
			{Q: 4, R: -3},
			{Q: 4, R: -2},
			{Q: 4, R: -1},
			{Q: 4, R: 0},
			{Q: 5, R: -7},
			{Q: 5, R: -6},
			{Q: 5, R: -5},
			{Q: 5, R: -4},
			{Q: 5, R: -3},
			{Q: 5, R: -2},
			{Q: 5, R: -1},
			{Q: 6, R: -5},
			{Q: 6, R: -4},
		},
		MineCount: 10,
		NeighborMap: map[string][]HexCoord{
			"2,-1": {{Q: 3, R: -1}, {Q: 3, R: -2}, {Q: 2, R: -2}, {Q: 1, R: -1}, {Q: 1, R: 0}, {Q: 2, R: 0}},
			"2,0":  {{Q: 3, R: 0}, {Q: 3, R: -1}, {Q: 2, R: -1}, {Q: 1, R: 0}, {Q: 1, R: 1}},
			"2,-2": {{Q: 3, R: -2}, {Q: 3, R: -3}, {Q: 2, R: -3}, {Q: 1, R: -2}, {Q: 1, R: -1}, {Q: 2, R: -1}},
			"3,-3": {{Q: 4, R: -3}, {Q: 4, R: -4}, {Q: 3, R: -4}, {Q: 2, R: -3}, {Q: 2, R: -2}, {Q: 3, R: -2}},
			"3,-2": {{Q: 4, R: -2}, {Q: 4, R: -3}, {Q: 3, R: -3}, {Q: 2, R: -2}, {Q: 2, R: -1}, {Q: 3, R: -1}},
			"3,-1": {{Q: 4, R: -1}, {Q: 4, R: -2}, {Q: 3, R: -2}, {Q: 2, R: -1}, {Q: 2, R: 0}, {Q: 3, R: 0}},
			"4,-3": {{Q: 5, R: -3}, {Q: 5, R: -4}, {Q: 4, R: -4}, {Q: 3, R: -3}, {Q: 3, R: -2}, {Q: 4, R: -2}},
			"4,-2": {{Q: 5, R: -2}, {Q: 5, R: -3}, {Q: 4, R: -3}, {Q: 3, R: -2}, {Q: 3, R: -1}, {Q: 4, R: -1}},
			"4,-1": {{Q: 5, R: -1}, {Q: 5, R: -2}, {Q: 4, R: -2}, {Q: 3, R: -1}, {Q: 3, R: 0}, {Q: 4, R: 0}},
			"2,-3": {{Q: 3, R: -3}, {Q: 3, R: -4}, {Q: 2, R: -4}, {Q: 1, R: -2}, {Q: 2, R: -2}},
			"3,0":  {{Q: 4, R: 0}, {Q: 4, R: -1}, {Q: 3, R: -1}, {Q: 2, R: 0}},
			"3,-4": {{Q: 4, R: -4}, {Q: 4, R: -5}, {Q: 3, R: -5}, {Q: 2, R: -4}, {Q: 2, R: -3}, {Q: 3, R: -3}},
			"4,-4": {{Q: 5, R: -4}, {Q: 5, R: -5}, {Q: 4, R: -5}, {Q: 3, R: -4}, {Q: 3, R: -3}, {Q: 4, R: -3}},
			"5,-4": {{Q: 6, R: -4}, {Q: 6, R: -5}, {Q: 5, R: -5}, {Q: 4, R: -4}, {Q: 4, R: -3}, {Q: 5, R: -3}},
			"4,-5": {{Q: 5, R: -5}, {Q: 5, R: -6}, {Q: 4, R: -6}, {Q: 3, R: -5}, {Q: 3, R: -4}, {Q: 4, R: -4}},
			"5,-5": {{Q: 6, R: -5}, {Q: 5, R: -6}, {Q: 4, R: -5}, {Q: 4, R: -4}, {Q: 5, R: -4}},
			"6,-5": {{Q: 5, R: -5}, {Q: 5, R: -4}, {Q: 6, R: -4}},
			"4,-6": {{Q: 5, R: -6}, {Q: 5, R: -7}, {Q: 4, R: -7}, {Q: 3, R: -6}, {Q: 3, R: -5}, {Q: 4, R: -5}},
			"5,-6": {{Q: 5, R: -7}, {Q: 4, R: -6}, {Q: 4, R: -5}, {Q: 5, R: -5}},
			"5,-7": {{Q: 4, R: -7}, {Q: 4, R: -6}, {Q: 5, R: -6}},
			"4,0":  {{Q: 5, R: -1}, {Q: 4, R: -1}, {Q: 3, R: 0}},
			"1,0":  {{Q: 2, R: 0}, {Q: 2, R: -1}, {Q: 1, R: -1}, {Q: 1, R: 1}},
			"1,1":  {{Q: 2, R: 0}, {Q: 1, R: 0}},
			"0,-1": {{Q: 1, R: -1}, {Q: 1, R: -2}},
			"1,-1": {{Q: 2, R: -1}, {Q: 2, R: -2}, {Q: 1, R: -2}, {Q: 0, R: -1}, {Q: 1, R: 0}},
			"5,-1": {{Q: 5, R: -2}, {Q: 4, R: -1}, {Q: 4, R: 0}},
			"1,-2": {{Q: 2, R: -2}, {Q: 2, R: -3}, {Q: 0, R: -1}, {Q: 1, R: -1}},
			"5,-2": {{Q: 5, R: -3}, {Q: 4, R: -2}, {Q: 4, R: -1}, {Q: 5, R: -1}},
			"5,-3": {{Q: 6, R: -4}, {Q: 5, R: -4}, {Q: 4, R: -3}, {Q: 4, R: -2}, {Q: 5, R: -2}},
			"6,-4": {{Q: 6, R: -5}, {Q: 5, R: -4}, {Q: 5, R: -3}},
			"2,-4": {{Q: 3, R: -4}, {Q: 3, R: -5}, {Q: 2, R: -5}, {Q: 1, R: -4}, {Q: 2, R: -3}},
			"1,-4": {{Q: 2, R: -4}, {Q: 2, R: -5}, {Q: 1, R: -5}},
			"1,-5": {{Q: 2, R: -5}, {Q: 2, R: -6}, {Q: 1, R: -4}},
			"2,-5": {{Q: 3, R: -5}, {Q: 3, R: -6}, {Q: 2, R: -6}, {Q: 1, R: -5}, {Q: 1, R: -4}, {Q: 2, R: -4}},
			"3,-5": {{Q: 4, R: -5}, {Q: 4, R: -6}, {Q: 3, R: -6}, {Q: 2, R: -5}, {Q: 2, R: -4}, {Q: 3, R: -4}},
			"3,-6": {{Q: 4, R: -6}, {Q: 4, R: -7}, {Q: 3, R: -7}, {Q: 2, R: -6}, {Q: 2, R: -5}, {Q: 3, R: -5}},
			"2,-6": {{Q: 3, R: -6}, {Q: 3, R: -7}, {Q: 1, R: -5}, {Q: 2, R: -5}},
			"4,-7": {{Q: 5, R: -7}, {Q: 3, R: -7}, {Q: 3, R: -6}, {Q: 4, R: -6}},
			"3,-7": {{Q: 4, R: -7}, {Q: 2, R: -6}, {Q: 3, R: -6}},
		},
	},

	// HexMapID: 3 - 第20关专用（15颗地雷）
	3: {
		MapID:   3,
		MapName: "04",
		ValidHexes: []HexCoord{
			{Q: 0, R: 0},
			{Q: 1, R: -2},
			{Q: 1, R: -1},
			{Q: 1, R: 0},
			{Q: 2, R: -4},
			{Q: 2, R: -3},
			{Q: 2, R: -2},
			{Q: 2, R: -1},
			{Q: 2, R: 0},
			{Q: 3, R: -6},
			{Q: 3, R: -5},
			{Q: 3, R: -4},
			{Q: 3, R: -3},
			{Q: 3, R: -2},
			{Q: 3, R: -1},
			{Q: 3, R: 0},
			{Q: 4, R: -7},
			{Q: 4, R: -6},
			{Q: 4, R: -5},
			{Q: 4, R: -4},
			{Q: 4, R: -3},
			{Q: 4, R: -2},
			{Q: 4, R: -1},
			{Q: 4, R: 0},
			{Q: 5, R: -7},
			{Q: 5, R: -6},
			{Q: 5, R: -5},
			{Q: 5, R: -4},
			{Q: 5, R: -3},
			{Q: 5, R: -2},
			{Q: 5, R: -1},
			{Q: 5, R: 0},
			{Q: 6, R: -7},
			{Q: 6, R: -6},
			{Q: 6, R: -5},
			{Q: 6, R: -4},
			{Q: 6, R: -3},
			{Q: 6, R: -2},
			{Q: 6, R: -1},
			{Q: 6, R: 0},
			{Q: 7, R: -7},
			{Q: 7, R: -6},
			{Q: 7, R: -5},
			{Q: 7, R: -4},
			{Q: 7, R: -3},
			{Q: 7, R: -2},
			{Q: 7, R: -1},
			{Q: 7, R: 0},
			{Q: 8, R: -7},
			{Q: 8, R: -6},
			{Q: 8, R: -5},
			{Q: 8, R: -4},
			{Q: 8, R: -3},
			{Q: 8, R: -2},
			{Q: 9, R: -7},
			{Q: 9, R: -6},
			{Q: 9, R: -5},
			{Q: 9, R: -4},
			{Q: 10, R: -7},
			{Q: 10, R: -6},
		},
		MineCount: 15,
		NeighborMap: map[string][]HexCoord{
			"0,0":   {{Q: 1, R: 0}, {Q: 1, R: -1}},
			"1,-2":  {{Q: 2, R: -2}, {Q: 2, R: -3}, {Q: 1, R: -1}},
			"1,-1":  {{Q: 2, R: -1}, {Q: 2, R: -2}, {Q: 1, R: -2}, {Q: 0, R: 0}, {Q: 1, R: 0}},
			"1,0":   {{Q: 2, R: 0}, {Q: 2, R: -1}, {Q: 1, R: -1}, {Q: 0, R: 0}},
			"2,-4":  {{Q: 3, R: -4}, {Q: 3, R: -5}, {Q: 2, R: -3}},
			"2,-3":  {{Q: 3, R: -3}, {Q: 3, R: -4}, {Q: 2, R: -4}, {Q: 1, R: -2}, {Q: 2, R: -2}},
			"2,-2":  {{Q: 3, R: -2}, {Q: 3, R: -3}, {Q: 2, R: -3}, {Q: 1, R: -2}, {Q: 1, R: -1}, {Q: 2, R: -1}},
			"2,-1":  {{Q: 3, R: -1}, {Q: 3, R: -2}, {Q: 2, R: -2}, {Q: 1, R: -1}, {Q: 1, R: 0}, {Q: 2, R: 0}},
			"2,0":   {{Q: 3, R: 0}, {Q: 3, R: -1}, {Q: 2, R: -1}, {Q: 1, R: 0}},
			"3,-6":  {{Q: 4, R: -6}, {Q: 4, R: -7}, {Q: 3, R: -5}},
			"3,-5":  {{Q: 4, R: -5}, {Q: 4, R: -6}, {Q: 3, R: -6}, {Q: 2, R: -4}, {Q: 3, R: -4}},
			"3,-4":  {{Q: 4, R: -4}, {Q: 4, R: -5}, {Q: 3, R: -5}, {Q: 2, R: -4}, {Q: 2, R: -3}, {Q: 3, R: -3}},
			"3,-3":  {{Q: 4, R: -3}, {Q: 4, R: -4}, {Q: 3, R: -4}, {Q: 2, R: -3}, {Q: 2, R: -2}, {Q: 3, R: -2}},
			"3,-2":  {{Q: 4, R: -2}, {Q: 4, R: -3}, {Q: 3, R: -3}, {Q: 2, R: -2}, {Q: 2, R: -1}, {Q: 3, R: -1}},
			"3,-1":  {{Q: 4, R: -1}, {Q: 4, R: -2}, {Q: 3, R: -2}, {Q: 2, R: -1}, {Q: 2, R: 0}, {Q: 3, R: 0}},
			"3,0":   {{Q: 4, R: 0}, {Q: 4, R: -1}, {Q: 3, R: -1}, {Q: 2, R: 0}},
			"4,-7":  {{Q: 5, R: -7}, {Q: 3, R: -6}, {Q: 4, R: -6}},
			"4,-6":  {{Q: 5, R: -6}, {Q: 5, R: -7}, {Q: 4, R: -7}, {Q: 3, R: -6}, {Q: 3, R: -5}, {Q: 4, R: -5}},
			"4,-5":  {{Q: 5, R: -5}, {Q: 5, R: -6}, {Q: 4, R: -6}, {Q: 3, R: -5}, {Q: 3, R: -4}, {Q: 4, R: -4}},
			"4,-4":  {{Q: 5, R: -4}, {Q: 5, R: -5}, {Q: 4, R: -5}, {Q: 3, R: -4}, {Q: 3, R: -3}, {Q: 4, R: -3}},
			"4,-3":  {{Q: 5, R: -3}, {Q: 5, R: -4}, {Q: 4, R: -4}, {Q: 3, R: -3}, {Q: 3, R: -2}, {Q: 4, R: -2}},
			"4,-2":  {{Q: 5, R: -2}, {Q: 5, R: -3}, {Q: 4, R: -3}, {Q: 3, R: -2}, {Q: 3, R: -1}, {Q: 4, R: -1}},
			"4,-1":  {{Q: 5, R: -1}, {Q: 5, R: -2}, {Q: 4, R: -2}, {Q: 3, R: -1}, {Q: 3, R: 0}, {Q: 4, R: 0}},
			"4,0":   {{Q: 5, R: 0}, {Q: 5, R: -1}, {Q: 4, R: -1}, {Q: 3, R: 0}},
			"5,-7":  {{Q: 6, R: -7}, {Q: 4, R: -7}, {Q: 4, R: -6}, {Q: 5, R: -6}},
			"5,-6":  {{Q: 6, R: -6}, {Q: 6, R: -7}, {Q: 5, R: -7}, {Q: 4, R: -6}, {Q: 4, R: -5}, {Q: 5, R: -5}},
			"5,-5":  {{Q: 6, R: -5}, {Q: 6, R: -6}, {Q: 5, R: -6}, {Q: 4, R: -5}, {Q: 4, R: -4}, {Q: 5, R: -4}},
			"5,-4":  {{Q: 6, R: -4}, {Q: 6, R: -5}, {Q: 5, R: -5}, {Q: 4, R: -4}, {Q: 4, R: -3}, {Q: 5, R: -3}},
			"5,-3":  {{Q: 6, R: -3}, {Q: 6, R: -4}, {Q: 5, R: -4}, {Q: 4, R: -3}, {Q: 4, R: -2}, {Q: 5, R: -2}},
			"5,-2":  {{Q: 6, R: -2}, {Q: 6, R: -3}, {Q: 5, R: -3}, {Q: 4, R: -2}, {Q: 4, R: -1}, {Q: 5, R: -1}},
			"5,-1":  {{Q: 6, R: -1}, {Q: 6, R: -2}, {Q: 5, R: -2}, {Q: 4, R: -1}, {Q: 4, R: 0}, {Q: 5, R: 0}},
			"5,0":   {{Q: 6, R: 0}, {Q: 6, R: -1}, {Q: 5, R: -1}, {Q: 4, R: 0}},
			"6,-7":  {{Q: 7, R: -7}, {Q: 5, R: -7}, {Q: 5, R: -6}, {Q: 6, R: -6}},
			"6,-6":  {{Q: 7, R: -6}, {Q: 7, R: -7}, {Q: 6, R: -7}, {Q: 5, R: -6}, {Q: 5, R: -5}, {Q: 6, R: -5}},
			"6,-5":  {{Q: 7, R: -5}, {Q: 7, R: -6}, {Q: 6, R: -6}, {Q: 5, R: -5}, {Q: 5, R: -4}, {Q: 6, R: -4}},
			"6,-4":  {{Q: 7, R: -4}, {Q: 7, R: -5}, {Q: 6, R: -5}, {Q: 5, R: -4}, {Q: 5, R: -3}, {Q: 6, R: -3}},
			"6,-3":  {{Q: 7, R: -3}, {Q: 7, R: -4}, {Q: 6, R: -4}, {Q: 5, R: -3}, {Q: 5, R: -2}, {Q: 6, R: -2}},
			"6,-2":  {{Q: 7, R: -2}, {Q: 7, R: -3}, {Q: 6, R: -3}, {Q: 5, R: -2}, {Q: 5, R: -1}, {Q: 6, R: -1}},
			"6,-1":  {{Q: 7, R: -1}, {Q: 7, R: -2}, {Q: 6, R: -2}, {Q: 5, R: -1}, {Q: 5, R: 0}, {Q: 6, R: 0}},
			"6,0":   {{Q: 7, R: 0}, {Q: 7, R: -1}, {Q: 6, R: -1}, {Q: 5, R: 0}},
			"7,-7":  {{Q: 8, R: -7}, {Q: 6, R: -7}, {Q: 6, R: -6}, {Q: 7, R: -6}},
			"7,-6":  {{Q: 8, R: -6}, {Q: 8, R: -7}, {Q: 7, R: -7}, {Q: 6, R: -6}, {Q: 6, R: -5}, {Q: 7, R: -5}},
			"7,-5":  {{Q: 8, R: -5}, {Q: 8, R: -6}, {Q: 7, R: -6}, {Q: 6, R: -5}, {Q: 6, R: -4}, {Q: 7, R: -4}},
			"7,-4":  {{Q: 8, R: -4}, {Q: 8, R: -5}, {Q: 7, R: -5}, {Q: 6, R: -4}, {Q: 6, R: -3}, {Q: 7, R: -3}},
			"7,-3":  {{Q: 8, R: -3}, {Q: 8, R: -4}, {Q: 7, R: -4}, {Q: 6, R: -3}, {Q: 6, R: -2}, {Q: 7, R: -2}},
			"7,-2":  {{Q: 8, R: -2}, {Q: 8, R: -3}, {Q: 7, R: -3}, {Q: 6, R: -2}, {Q: 6, R: -1}, {Q: 7, R: -1}},
			"7,-1":  {{Q: 8, R: -2}, {Q: 7, R: -2}, {Q: 6, R: -1}, {Q: 6, R: 0}, {Q: 7, R: 0}},
			"7,0":   {{Q: 7, R: -1}, {Q: 6, R: 0}},
			"8,-7":  {{Q: 9, R: -7}, {Q: 7, R: -7}, {Q: 7, R: -6}, {Q: 8, R: -6}},
			"8,-6":  {{Q: 9, R: -6}, {Q: 9, R: -7}, {Q: 8, R: -7}, {Q: 7, R: -6}, {Q: 7, R: -5}, {Q: 8, R: -5}},
			"8,-5":  {{Q: 9, R: -5}, {Q: 9, R: -6}, {Q: 8, R: -6}, {Q: 7, R: -5}, {Q: 7, R: -4}, {Q: 8, R: -4}},
			"8,-4":  {{Q: 9, R: -4}, {Q: 9, R: -5}, {Q: 8, R: -5}, {Q: 7, R: -4}, {Q: 7, R: -3}, {Q: 8, R: -3}},
			"8,-3":  {{Q: 9, R: -4}, {Q: 8, R: -4}, {Q: 7, R: -3}, {Q: 7, R: -2}, {Q: 8, R: -2}},
			"8,-2":  {{Q: 8, R: -3}, {Q: 7, R: -2}, {Q: 7, R: -1}},
			"9,-7":  {{Q: 10, R: -7}, {Q: 8, R: -7}, {Q: 8, R: -6}, {Q: 9, R: -6}},
			"9,-6":  {{Q: 10, R: -6}, {Q: 10, R: -7}, {Q: 9, R: -7}, {Q: 8, R: -6}, {Q: 8, R: -5}, {Q: 9, R: -5}},
			"9,-5":  {{Q: 10, R: -6}, {Q: 9, R: -6}, {Q: 8, R: -5}, {Q: 8, R: -4}, {Q: 9, R: -4}},
			"9,-4":  {{Q: 9, R: -5}, {Q: 8, R: -4}, {Q: 8, R: -3}},
			"10,-7": {{Q: 9, R: -7}, {Q: 9, R: -6}, {Q: 10, R: -6}},
			"10,-6": {{Q: 10, R: -7}, {Q: 9, R: -6}, {Q: 9, R: -5}},
		},
	},

	// HexMapID: 4 - 第25关专用（18颗地雷）
	4: {
		MapID:   4,
		MapName: "05",
		ValidHexes: []HexCoord{
			{Q: 0, R: -1},
			{Q: 0, R: 0},
			{Q: 1, R: -3},
			{Q: 1, R: -2},
			{Q: 1, R: -1},
			{Q: 1, R: 0},
			{Q: 2, R: -5},
			{Q: 2, R: -4},
			{Q: 2, R: -3},
			{Q: 2, R: -2},
			{Q: 2, R: -1},
			{Q: 2, R: 0},
			{Q: 3, R: -7},
			{Q: 3, R: -6},
			{Q: 3, R: -5},
			{Q: 3, R: -4},
			{Q: 3, R: -3},
			{Q: 3, R: -2},
			{Q: 3, R: -1},
			{Q: 3, R: 0},
			{Q: 4, R: -8},
			{Q: 4, R: -7},
			{Q: 4, R: -6},
			{Q: 4, R: -5},
			{Q: 4, R: -4},
			{Q: 4, R: -3},
			{Q: 4, R: -2},
			{Q: 4, R: -1},
			{Q: 4, R: 0},
			{Q: 5, R: -8},
			{Q: 5, R: -7},
			{Q: 5, R: -6},
			{Q: 5, R: -5},
			{Q: 5, R: -3},
			{Q: 5, R: -2},
			{Q: 5, R: -1},
			{Q: 5, R: 0},
			{Q: 6, R: -8},
			{Q: 6, R: -7},
			{Q: 6, R: -6},
			{Q: 6, R: -5},
			{Q: 6, R: -4},
			{Q: 6, R: -3},
			{Q: 6, R: -2},
			{Q: 6, R: -1},
			{Q: 6, R: 0},
			{Q: 7, R: -8},
			{Q: 7, R: -7},
			{Q: 7, R: -6},
			{Q: 7, R: -5},
			{Q: 7, R: -4},
			{Q: 7, R: -3},
			{Q: 7, R: -2},
			{Q: 7, R: -1},
			{Q: 8, R: -8},
			{Q: 8, R: -7},
			{Q: 8, R: -6},
			{Q: 8, R: -5},
			{Q: 8, R: -4},
			{Q: 8, R: -3},
			{Q: 9, R: -8},
			{Q: 9, R: -7},
			{Q: 9, R: -6},
			{Q: 9, R: -5},
			{Q: 10, R: -8},
			{Q: 10, R: -7},
		},
		MineCount: 18,
		NeighborMap: map[string][]HexCoord{
			"0,0":   {{Q: 1, R: 0}, {Q: 1, R: -1}, {Q: 0, R: -1}},
			"1,-2":  {{Q: 2, R: -2}, {Q: 2, R: -3}, {Q: 1, R: -3}, {Q: 0, R: -1}, {Q: 1, R: -1}},
			"1,-1":  {{Q: 2, R: -1}, {Q: 2, R: -2}, {Q: 1, R: -2}, {Q: 0, R: -1}, {Q: 0, R: 0}, {Q: 1, R: 0}},
			"1,0":   {{Q: 2, R: 0}, {Q: 2, R: -1}, {Q: 1, R: -1}, {Q: 0, R: 0}},
			"2,-4":  {{Q: 3, R: -4}, {Q: 3, R: -5}, {Q: 2, R: -5}, {Q: 1, R: -3}, {Q: 2, R: -3}},
			"2,-3":  {{Q: 3, R: -3}, {Q: 3, R: -4}, {Q: 2, R: -4}, {Q: 1, R: -3}, {Q: 1, R: -2}, {Q: 2, R: -2}},
			"2,-2":  {{Q: 3, R: -2}, {Q: 3, R: -3}, {Q: 2, R: -3}, {Q: 1, R: -2}, {Q: 1, R: -1}, {Q: 2, R: -1}},
			"2,-1":  {{Q: 3, R: -1}, {Q: 3, R: -2}, {Q: 2, R: -2}, {Q: 1, R: -1}, {Q: 1, R: 0}, {Q: 2, R: 0}},
			"2,0":   {{Q: 3, R: 0}, {Q: 3, R: -1}, {Q: 2, R: -1}, {Q: 1, R: 0}},
			"3,-6":  {{Q: 4, R: -6}, {Q: 4, R: -7}, {Q: 3, R: -7}, {Q: 2, R: -5}, {Q: 3, R: -5}},
			"3,-5":  {{Q: 4, R: -5}, {Q: 4, R: -6}, {Q: 3, R: -6}, {Q: 2, R: -5}, {Q: 2, R: -4}, {Q: 3, R: -4}},
			"3,-4":  {{Q: 4, R: -4}, {Q: 4, R: -5}, {Q: 3, R: -5}, {Q: 2, R: -4}, {Q: 2, R: -3}, {Q: 3, R: -3}},
			"3,-3":  {{Q: 4, R: -3}, {Q: 4, R: -4}, {Q: 3, R: -4}, {Q: 2, R: -3}, {Q: 2, R: -2}, {Q: 3, R: -2}},
			"3,-2":  {{Q: 4, R: -2}, {Q: 4, R: -3}, {Q: 3, R: -3}, {Q: 2, R: -2}, {Q: 2, R: -1}, {Q: 3, R: -1}},
			"3,-1":  {{Q: 4, R: -1}, {Q: 4, R: -2}, {Q: 3, R: -2}, {Q: 2, R: -1}, {Q: 2, R: 0}, {Q: 3, R: 0}},
			"3,0":   {{Q: 4, R: 0}, {Q: 4, R: -1}, {Q: 3, R: -1}, {Q: 2, R: 0}},
			"4,-7":  {{Q: 5, R: -7}, {Q: 5, R: -8}, {Q: 4, R: -8}, {Q: 3, R: -7}, {Q: 3, R: -6}, {Q: 4, R: -6}},
			"4,-6":  {{Q: 5, R: -6}, {Q: 5, R: -7}, {Q: 4, R: -7}, {Q: 3, R: -6}, {Q: 3, R: -5}, {Q: 4, R: -5}},
			"4,-5":  {{Q: 5, R: -5}, {Q: 5, R: -6}, {Q: 4, R: -6}, {Q: 3, R: -5}, {Q: 3, R: -4}, {Q: 4, R: -4}},
			"4,-4":  {{Q: 5, R: -5}, {Q: 4, R: -5}, {Q: 3, R: -4}, {Q: 3, R: -3}, {Q: 4, R: -3}},
			"4,-3":  {{Q: 5, R: -3}, {Q: 4, R: -4}, {Q: 3, R: -3}, {Q: 3, R: -2}, {Q: 4, R: -2}},
			"4,-2":  {{Q: 5, R: -2}, {Q: 5, R: -3}, {Q: 4, R: -3}, {Q: 3, R: -2}, {Q: 3, R: -1}, {Q: 4, R: -1}},
			"4,-1":  {{Q: 5, R: -1}, {Q: 5, R: -2}, {Q: 4, R: -2}, {Q: 3, R: -1}, {Q: 3, R: 0}, {Q: 4, R: 0}},
			"4,0":   {{Q: 5, R: 0}, {Q: 5, R: -1}, {Q: 4, R: -1}, {Q: 3, R: 0}},
			"5,-7":  {{Q: 6, R: -7}, {Q: 6, R: -8}, {Q: 5, R: -8}, {Q: 4, R: -7}, {Q: 4, R: -6}, {Q: 5, R: -6}},
			"5,-6":  {{Q: 6, R: -6}, {Q: 6, R: -7}, {Q: 5, R: -7}, {Q: 4, R: -6}, {Q: 4, R: -5}, {Q: 5, R: -5}},
			"5,-5":  {{Q: 6, R: -5}, {Q: 6, R: -6}, {Q: 5, R: -6}, {Q: 4, R: -5}, {Q: 4, R: -4}},
			"5,-3":  {{Q: 6, R: -3}, {Q: 6, R: -4}, {Q: 4, R: -3}, {Q: 4, R: -2}, {Q: 5, R: -2}},
			"5,-2":  {{Q: 6, R: -2}, {Q: 6, R: -3}, {Q: 5, R: -3}, {Q: 4, R: -2}, {Q: 4, R: -1}, {Q: 5, R: -1}},
			"5,-1":  {{Q: 6, R: -1}, {Q: 6, R: -2}, {Q: 5, R: -2}, {Q: 4, R: -1}, {Q: 4, R: 0}, {Q: 5, R: 0}},
			"5,0":   {{Q: 6, R: 0}, {Q: 6, R: -1}, {Q: 5, R: -1}, {Q: 4, R: 0}},
			"6,-7":  {{Q: 7, R: -7}, {Q: 7, R: -8}, {Q: 6, R: -8}, {Q: 5, R: -7}, {Q: 5, R: -6}, {Q: 6, R: -6}},
			"6,-6":  {{Q: 7, R: -6}, {Q: 7, R: -7}, {Q: 6, R: -7}, {Q: 5, R: -6}, {Q: 5, R: -5}, {Q: 6, R: -5}},
			"6,-5":  {{Q: 7, R: -5}, {Q: 7, R: -6}, {Q: 6, R: -6}, {Q: 5, R: -5}, {Q: 6, R: -4}},
			"6,-4":  {{Q: 7, R: -4}, {Q: 7, R: -5}, {Q: 6, R: -5}, {Q: 5, R: -3}, {Q: 6, R: -3}},
			"6,-3":  {{Q: 7, R: -3}, {Q: 7, R: -4}, {Q: 6, R: -4}, {Q: 5, R: -3}, {Q: 5, R: -2}, {Q: 6, R: -2}},
			"6,-2":  {{Q: 7, R: -2}, {Q: 7, R: -3}, {Q: 6, R: -3}, {Q: 5, R: -2}, {Q: 5, R: -1}, {Q: 6, R: -1}},
			"6,-1":  {{Q: 7, R: -1}, {Q: 7, R: -2}, {Q: 6, R: -2}, {Q: 5, R: -1}, {Q: 5, R: 0}, {Q: 6, R: 0}},
			"6,0":   {{Q: 7, R: -1}, {Q: 6, R: -1}, {Q: 5, R: 0}},
			"7,-7":  {{Q: 8, R: -7}, {Q: 8, R: -8}, {Q: 7, R: -8}, {Q: 6, R: -7}, {Q: 6, R: -6}, {Q: 7, R: -6}},
			"7,-6":  {{Q: 8, R: -6}, {Q: 8, R: -7}, {Q: 7, R: -7}, {Q: 6, R: -6}, {Q: 6, R: -5}, {Q: 7, R: -5}},
			"7,-5":  {{Q: 8, R: -5}, {Q: 8, R: -6}, {Q: 7, R: -6}, {Q: 6, R: -5}, {Q: 6, R: -4}, {Q: 7, R: -4}},
			"7,-4":  {{Q: 8, R: -4}, {Q: 8, R: -5}, {Q: 7, R: -5}, {Q: 6, R: -4}, {Q: 6, R: -3}, {Q: 7, R: -3}},
			"7,-3":  {{Q: 8, R: -3}, {Q: 8, R: -4}, {Q: 7, R: -4}, {Q: 6, R: -3}, {Q: 6, R: -2}, {Q: 7, R: -2}},
			"7,-2":  {{Q: 8, R: -3}, {Q: 7, R: -3}, {Q: 6, R: -2}, {Q: 6, R: -1}, {Q: 7, R: -1}},
			"8,-7":  {{Q: 9, R: -7}, {Q: 9, R: -8}, {Q: 8, R: -8}, {Q: 7, R: -7}, {Q: 7, R: -6}, {Q: 8, R: -6}},
			"8,-6":  {{Q: 9, R: -6}, {Q: 9, R: -7}, {Q: 8, R: -7}, {Q: 7, R: -6}, {Q: 7, R: -5}, {Q: 8, R: -5}},
			"8,-5":  {{Q: 9, R: -5}, {Q: 9, R: -6}, {Q: 8, R: -6}, {Q: 7, R: -5}, {Q: 7, R: -4}, {Q: 8, R: -4}},
			"8,-4":  {{Q: 9, R: -5}, {Q: 8, R: -5}, {Q: 7, R: -4}, {Q: 7, R: -3}, {Q: 8, R: -3}},
			"9,-7":  {{Q: 10, R: -7}, {Q: 10, R: -8}, {Q: 9, R: -8}, {Q: 8, R: -7}, {Q: 8, R: -6}, {Q: 9, R: -6}},
			"9,-6":  {{Q: 10, R: -7}, {Q: 9, R: -7}, {Q: 8, R: -6}, {Q: 8, R: -5}, {Q: 9, R: -5}},
			"0,-1":  {{Q: 1, R: -1}, {Q: 1, R: -2}, {Q: 0, R: 0}},
			"7,-1":  {{Q: 7, R: -2}, {Q: 6, R: -1}, {Q: 6, R: 0}},
			"8,-3":  {{Q: 8, R: -4}, {Q: 7, R: -3}, {Q: 7, R: -2}},
			"9,-5":  {{Q: 9, R: -6}, {Q: 8, R: -5}, {Q: 8, R: -4}},
			"10,-7": {{Q: 10, R: -8}, {Q: 9, R: -7}, {Q: 9, R: -6}},
			"10,-8": {{Q: 9, R: -8}, {Q: 9, R: -7}, {Q: 10, R: -7}},
			"9,-8":  {{Q: 10, R: -8}, {Q: 8, R: -8}, {Q: 8, R: -7}, {Q: 9, R: -7}},
			"8,-8":  {{Q: 9, R: -8}, {Q: 7, R: -8}, {Q: 7, R: -7}, {Q: 8, R: -7}},
			"7,-8":  {{Q: 8, R: -8}, {Q: 6, R: -8}, {Q: 6, R: -7}, {Q: 7, R: -7}},
			"6,-8":  {{Q: 7, R: -8}, {Q: 5, R: -8}, {Q: 5, R: -7}, {Q: 6, R: -7}},
			"5,-8":  {{Q: 6, R: -8}, {Q: 4, R: -8}, {Q: 4, R: -7}, {Q: 5, R: -7}},
			"4,-8":  {{Q: 5, R: -8}, {Q: 3, R: -7}, {Q: 4, R: -7}},
			"1,-3":  {{Q: 2, R: -3}, {Q: 2, R: -4}, {Q: 1, R: -2}},
			"2,-5":  {{Q: 3, R: -5}, {Q: 3, R: -6}, {Q: 2, R: -4}},
			"3,-7":  {{Q: 4, R: -7}, {Q: 4, R: -8}, {Q: 3, R: -6}},
		},
	},

	// HexMapID: 5 - 第30关专用（25颗地雷）
	5: {
		MapID:   5,
		MapName: "06",
		ValidHexes: []HexCoord{
			{Q: 0, R: 0},
			{Q: 1, R: -2},
			{Q: 1, R: -1},
			{Q: 1, R: 0},
			{Q: 2, R: -4},
			{Q: 2, R: -3},
			{Q: 2, R: -2},
			{Q: 2, R: -1},
			{Q: 2, R: 0},
			{Q: 3, R: -6},
			{Q: 3, R: -5},
			{Q: 3, R: -4},
			{Q: 3, R: -3},
			{Q: 3, R: -2},
			{Q: 3, R: -1},
			{Q: 3, R: 0},
			{Q: 4, R: -8},
			{Q: 4, R: -7},
			{Q: 4, R: -6},
			{Q: 4, R: -5},
			{Q: 4, R: -4},
			{Q: 4, R: -3},
			{Q: 4, R: -2},
			{Q: 4, R: -1},
			{Q: 4, R: 0},
			{Q: 5, R: -9},
			{Q: 5, R: -8},
			{Q: 5, R: -7},
			{Q: 5, R: -6},
			{Q: 5, R: -5},
			{Q: 5, R: -4},
			{Q: 5, R: -3},
			{Q: 5, R: -2},
			{Q: 5, R: -1},
			{Q: 5, R: 0},
			{Q: 6, R: -9},
			{Q: 6, R: -8},
			{Q: 6, R: -7},
			{Q: 6, R: -6},
			{Q: 6, R: -5},
			{Q: 6, R: -4},
			{Q: 6, R: -3},
			{Q: 6, R: -2},
			{Q: 6, R: -1},
			{Q: 6, R: 0},
			{Q: 7, R: -9},
			{Q: 7, R: -8},
			{Q: 7, R: -7},
			{Q: 7, R: -6},
			{Q: 7, R: -5},
			{Q: 7, R: -4},
			{Q: 7, R: -3},
			{Q: 7, R: -2},
			{Q: 7, R: -1},
			{Q: 7, R: 0},
			{Q: 8, R: -9},
			{Q: 8, R: -8},
			{Q: 8, R: -7},
			{Q: 8, R: -6},
			{Q: 8, R: -5},
			{Q: 8, R: -4},
			{Q: 8, R: -3},
			{Q: 8, R: -2},
			{Q: 8, R: -1},
			{Q: 8, R: 0},
			{Q: 9, R: -9},
			{Q: 9, R: -8},
			{Q: 9, R: -7},
			{Q: 9, R: -6},
			{Q: 9, R: -5},
			{Q: 9, R: -4},
			{Q: 9, R: -3},
			{Q: 9, R: -2},
			{Q: 10, R: -9},
			{Q: 10, R: -8},
			{Q: 10, R: -7},
			{Q: 10, R: -6},
			{Q: 10, R: -5},
			{Q: 10, R: -4},
			{Q: 11, R: -9},
			{Q: 11, R: -8},
			{Q: 11, R: -7},
			{Q: 11, R: -6},
			{Q: 12, R: -9},
			{Q: 12, R: -8},
		},
		MineCount: 21,
		NeighborMap: map[string][]HexCoord{
			"0,0":   {{Q: 1, R: 0}, {Q: 1, R: -1}},
			"1,-2":  {{Q: 2, R: -2}, {Q: 2, R: -3}, {Q: 1, R: -1}},
			"1,-1":  {{Q: 2, R: -1}, {Q: 2, R: -2}, {Q: 1, R: -2}, {Q: 0, R: 0}, {Q: 1, R: 0}},
			"1,0":   {{Q: 2, R: 0}, {Q: 2, R: -1}, {Q: 1, R: -1}, {Q: 0, R: 0}},
			"2,-4":  {{Q: 3, R: -4}, {Q: 3, R: -5}, {Q: 2, R: -3}},
			"2,-3":  {{Q: 3, R: -3}, {Q: 3, R: -4}, {Q: 2, R: -4}, {Q: 1, R: -2}, {Q: 2, R: -2}},
			"2,-2":  {{Q: 3, R: -2}, {Q: 3, R: -3}, {Q: 2, R: -3}, {Q: 1, R: -2}, {Q: 1, R: -1}, {Q: 2, R: -1}},
			"2,-1":  {{Q: 3, R: -1}, {Q: 3, R: -2}, {Q: 2, R: -2}, {Q: 1, R: -1}, {Q: 1, R: 0}, {Q: 2, R: 0}},
			"2,0":   {{Q: 3, R: 0}, {Q: 3, R: -1}, {Q: 2, R: -1}, {Q: 1, R: 0}},
			"3,-6":  {{Q: 4, R: -6}, {Q: 4, R: -7}, {Q: 3, R: -5}},
			"3,-5":  {{Q: 4, R: -5}, {Q: 4, R: -6}, {Q: 3, R: -6}, {Q: 2, R: -4}, {Q: 3, R: -4}},
			"3,-4":  {{Q: 4, R: -4}, {Q: 4, R: -5}, {Q: 3, R: -5}, {Q: 2, R: -4}, {Q: 2, R: -3}, {Q: 3, R: -3}},
			"3,-3":  {{Q: 4, R: -3}, {Q: 4, R: -4}, {Q: 3, R: -4}, {Q: 2, R: -3}, {Q: 2, R: -2}, {Q: 3, R: -2}},
			"3,-2":  {{Q: 4, R: -2}, {Q: 4, R: -3}, {Q: 3, R: -3}, {Q: 2, R: -2}, {Q: 2, R: -1}, {Q: 3, R: -1}},
			"3,-1":  {{Q: 4, R: -1}, {Q: 4, R: -2}, {Q: 3, R: -2}, {Q: 2, R: -1}, {Q: 2, R: 0}, {Q: 3, R: 0}},
			"3,0":   {{Q: 4, R: 0}, {Q: 4, R: -1}, {Q: 3, R: -1}, {Q: 2, R: 0}},
			"4,-7":  {{Q: 5, R: -7}, {Q: 5, R: -8}, {Q: 4, R: -8}, {Q: 3, R: -6}, {Q: 4, R: -6}},
			"4,-6":  {{Q: 5, R: -6}, {Q: 5, R: -7}, {Q: 4, R: -7}, {Q: 3, R: -6}, {Q: 3, R: -5}, {Q: 4, R: -5}},
			"4,-5":  {{Q: 5, R: -5}, {Q: 5, R: -6}, {Q: 4, R: -6}, {Q: 3, R: -5}, {Q: 3, R: -4}, {Q: 4, R: -4}},
			"4,-4":  {{Q: 5, R: -4}, {Q: 5, R: -5}, {Q: 4, R: -5}, {Q: 3, R: -4}, {Q: 3, R: -3}, {Q: 4, R: -3}},
			"4,-3":  {{Q: 5, R: -3}, {Q: 5, R: -4}, {Q: 4, R: -4}, {Q: 3, R: -3}, {Q: 3, R: -2}, {Q: 4, R: -2}},
			"4,-2":  {{Q: 5, R: -2}, {Q: 5, R: -3}, {Q: 4, R: -3}, {Q: 3, R: -2}, {Q: 3, R: -1}, {Q: 4, R: -1}},
			"4,-1":  {{Q: 5, R: -1}, {Q: 5, R: -2}, {Q: 4, R: -2}, {Q: 3, R: -1}, {Q: 3, R: 0}, {Q: 4, R: 0}},
			"4,0":   {{Q: 5, R: 0}, {Q: 5, R: -1}, {Q: 4, R: -1}, {Q: 3, R: 0}},
			"5,-7":  {{Q: 6, R: -7}, {Q: 6, R: -8}, {Q: 5, R: -8}, {Q: 4, R: -7}, {Q: 4, R: -6}, {Q: 5, R: -6}},
			"5,-6":  {{Q: 6, R: -6}, {Q: 6, R: -7}, {Q: 5, R: -7}, {Q: 4, R: -6}, {Q: 4, R: -5}, {Q: 5, R: -5}},
			"5,-5":  {{Q: 6, R: -5}, {Q: 6, R: -6}, {Q: 5, R: -6}, {Q: 4, R: -5}, {Q: 4, R: -4}, {Q: 5, R: -4}},
			"5,-3":  {{Q: 6, R: -3}, {Q: 6, R: -4}, {Q: 5, R: -4}, {Q: 4, R: -3}, {Q: 4, R: -2}, {Q: 5, R: -2}},
			"5,-2":  {{Q: 6, R: -2}, {Q: 6, R: -3}, {Q: 5, R: -3}, {Q: 4, R: -2}, {Q: 4, R: -1}, {Q: 5, R: -1}},
			"5,-1":  {{Q: 6, R: -1}, {Q: 6, R: -2}, {Q: 5, R: -2}, {Q: 4, R: -1}, {Q: 4, R: 0}, {Q: 5, R: 0}},
			"5,0":   {{Q: 6, R: 0}, {Q: 6, R: -1}, {Q: 5, R: -1}, {Q: 4, R: 0}},
			"6,-7":  {{Q: 7, R: -7}, {Q: 7, R: -8}, {Q: 6, R: -8}, {Q: 5, R: -7}, {Q: 5, R: -6}, {Q: 6, R: -6}},
			"6,-6":  {{Q: 7, R: -6}, {Q: 7, R: -7}, {Q: 6, R: -7}, {Q: 5, R: -6}, {Q: 5, R: -5}, {Q: 6, R: -5}},
			"6,-5":  {{Q: 7, R: -5}, {Q: 7, R: -6}, {Q: 6, R: -6}, {Q: 5, R: -5}, {Q: 5, R: -4}, {Q: 6, R: -4}},
			"6,-4":  {{Q: 7, R: -4}, {Q: 7, R: -5}, {Q: 6, R: -5}, {Q: 5, R: -4}, {Q: 5, R: -3}, {Q: 6, R: -3}},
			"6,-3":  {{Q: 7, R: -3}, {Q: 7, R: -4}, {Q: 6, R: -4}, {Q: 5, R: -3}, {Q: 5, R: -2}, {Q: 6, R: -2}},
			"6,-2":  {{Q: 7, R: -2}, {Q: 7, R: -3}, {Q: 6, R: -3}, {Q: 5, R: -2}, {Q: 5, R: -1}, {Q: 6, R: -1}},
			"6,-1":  {{Q: 7, R: -1}, {Q: 7, R: -2}, {Q: 6, R: -2}, {Q: 5, R: -1}, {Q: 5, R: 0}, {Q: 6, R: 0}},
			"6,0":   {{Q: 7, R: 0}, {Q: 7, R: -1}, {Q: 6, R: -1}, {Q: 5, R: 0}},
			"7,-7":  {{Q: 8, R: -7}, {Q: 8, R: -8}, {Q: 7, R: -8}, {Q: 6, R: -7}, {Q: 6, R: -6}, {Q: 7, R: -6}},
			"7,-6":  {{Q: 8, R: -6}, {Q: 8, R: -7}, {Q: 7, R: -7}, {Q: 6, R: -6}, {Q: 6, R: -5}, {Q: 7, R: -5}},
			"7,-5":  {{Q: 8, R: -5}, {Q: 8, R: -6}, {Q: 7, R: -6}, {Q: 6, R: -5}, {Q: 6, R: -4}, {Q: 7, R: -4}},
			"7,-4":  {{Q: 8, R: -4}, {Q: 8, R: -5}, {Q: 7, R: -5}, {Q: 6, R: -4}, {Q: 6, R: -3}, {Q: 7, R: -3}},
			"7,-3":  {{Q: 8, R: -3}, {Q: 8, R: -4}, {Q: 7, R: -4}, {Q: 6, R: -3}, {Q: 6, R: -2}, {Q: 7, R: -2}},
			"7,-2":  {{Q: 8, R: -2}, {Q: 8, R: -3}, {Q: 7, R: -3}, {Q: 6, R: -2}, {Q: 6, R: -1}, {Q: 7, R: -1}},
			"8,-7":  {{Q: 9, R: -7}, {Q: 9, R: -8}, {Q: 8, R: -8}, {Q: 7, R: -7}, {Q: 7, R: -6}, {Q: 8, R: -6}},
			"8,-6":  {{Q: 9, R: -6}, {Q: 9, R: -7}, {Q: 8, R: -7}, {Q: 7, R: -6}, {Q: 7, R: -5}, {Q: 8, R: -5}},
			"8,-5":  {{Q: 9, R: -5}, {Q: 9, R: -6}, {Q: 8, R: -6}, {Q: 7, R: -5}, {Q: 7, R: -4}, {Q: 8, R: -4}},
			"8,-4":  {{Q: 9, R: -4}, {Q: 9, R: -5}, {Q: 8, R: -5}, {Q: 7, R: -4}, {Q: 7, R: -3}, {Q: 8, R: -3}},
			"9,-7":  {{Q: 10, R: -7}, {Q: 10, R: -8}, {Q: 9, R: -8}, {Q: 8, R: -7}, {Q: 8, R: -6}, {Q: 9, R: -6}},
			"9,-6":  {{Q: 10, R: -6}, {Q: 10, R: -7}, {Q: 9, R: -7}, {Q: 8, R: -6}, {Q: 8, R: -5}, {Q: 9, R: -5}},
			"7,-1":  {{Q: 8, R: -1}, {Q: 8, R: -2}, {Q: 7, R: -2}, {Q: 6, R: -1}, {Q: 6, R: 0}, {Q: 7, R: 0}},
			"8,-3":  {{Q: 9, R: -3}, {Q: 9, R: -4}, {Q: 8, R: -4}, {Q: 7, R: -3}, {Q: 7, R: -2}, {Q: 8, R: -2}},
			"9,-5":  {{Q: 10, R: -5}, {Q: 10, R: -6}, {Q: 9, R: -6}, {Q: 8, R: -5}, {Q: 8, R: -4}, {Q: 9, R: -4}},
			"10,-7": {{Q: 11, R: -7}, {Q: 11, R: -8}, {Q: 10, R: -8}, {Q: 9, R: -7}, {Q: 9, R: -6}, {Q: 10, R: -6}},
			"10,-8": {{Q: 11, R: -8}, {Q: 11, R: -9}, {Q: 10, R: -9}, {Q: 9, R: -8}, {Q: 9, R: -7}, {Q: 10, R: -7}},
			"9,-8":  {{Q: 10, R: -8}, {Q: 10, R: -9}, {Q: 9, R: -9}, {Q: 8, R: -8}, {Q: 8, R: -7}, {Q: 9, R: -7}},
			"8,-8":  {{Q: 9, R: -8}, {Q: 9, R: -9}, {Q: 8, R: -9}, {Q: 7, R: -8}, {Q: 7, R: -7}, {Q: 8, R: -7}},
			"7,-8":  {{Q: 8, R: -8}, {Q: 8, R: -9}, {Q: 7, R: -9}, {Q: 6, R: -8}, {Q: 6, R: -7}, {Q: 7, R: -7}},
			"6,-8":  {{Q: 7, R: -8}, {Q: 7, R: -9}, {Q: 6, R: -9}, {Q: 5, R: -8}, {Q: 5, R: -7}, {Q: 6, R: -7}},
			"5,-8":  {{Q: 6, R: -8}, {Q: 6, R: -9}, {Q: 5, R: -9}, {Q: 4, R: -8}, {Q: 4, R: -7}, {Q: 5, R: -7}},
			"4,-8":  {{Q: 5, R: -8}, {Q: 5, R: -9}, {Q: 4, R: -7}},
			"7,0":   {{Q: 8, R: 0}, {Q: 8, R: -1}, {Q: 7, R: -1}, {Q: 6, R: 0}},
			"8,0":   {{Q: 8, R: -1}, {Q: 7, R: 0}},
			"8,-1":  {{Q: 9, R: -2}, {Q: 8, R: -2}, {Q: 7, R: -1}, {Q: 7, R: 0}, {Q: 8, R: 0}},
			"8,-2":  {{Q: 9, R: -2}, {Q: 9, R: -3}, {Q: 8, R: -3}, {Q: 7, R: -2}, {Q: 7, R: -1}, {Q: 8, R: -1}},
			"9,-3":  {{Q: 10, R: -4}, {Q: 9, R: -4}, {Q: 8, R: -3}, {Q: 8, R: -2}, {Q: 9, R: -2}},
			"9,-4":  {{Q: 10, R: -4}, {Q: 10, R: -5}, {Q: 9, R: -5}, {Q: 8, R: -4}, {Q: 8, R: -3}, {Q: 9, R: -3}},
			"10,-5": {{Q: 11, R: -6}, {Q: 10, R: -6}, {Q: 9, R: -5}, {Q: 9, R: -4}, {Q: 10, R: -4}},
			"10,-6": {{Q: 11, R: -6}, {Q: 11, R: -7}, {Q: 10, R: -7}, {Q: 9, R: -6}, {Q: 9, R: -5}, {Q: 10, R: -5}},
			"11,-7": {{Q: 12, R: -8}, {Q: 11, R: -8}, {Q: 10, R: -7}, {Q: 10, R: -6}, {Q: 11, R: -6}},
			"11,-8": {{Q: 12, R: -8}, {Q: 12, R: -9}, {Q: 11, R: -9}, {Q: 10, R: -8}, {Q: 10, R: -7}, {Q: 11, R: -7}},
			"5,-9":  {{Q: 6, R: -9}, {Q: 4, R: -8}, {Q: 5, R: -8}},
			"6,-9":  {{Q: 7, R: -9}, {Q: 5, R: -9}, {Q: 5, R: -8}, {Q: 6, R: -8}},
			"7,-9":  {{Q: 8, R: -9}, {Q: 6, R: -9}, {Q: 6, R: -8}, {Q: 7, R: -8}},
			"8,-9":  {{Q: 9, R: -9}, {Q: 7, R: -9}, {Q: 7, R: -8}, {Q: 8, R: -8}},
			"9,-9":  {{Q: 10, R: -9}, {Q: 8, R: -9}, {Q: 8, R: -8}, {Q: 9, R: -8}},
			"10,-9": {{Q: 11, R: -9}, {Q: 9, R: -9}, {Q: 9, R: -8}, {Q: 10, R: -8}},
			"11,-9": {{Q: 12, R: -9}, {Q: 10, R: -9}, {Q: 10, R: -8}, {Q: 11, R: -8}},
			"12,-9": {{Q: 11, R: -9}, {Q: 11, R: -8}, {Q: 12, R: -8}},
			"9,-2":  {{Q: 9, R: -3}, {Q: 8, R: -2}, {Q: 8, R: -1}},
			"10,-4": {{Q: 10, R: -5}, {Q: 9, R: -4}, {Q: 9, R: -3}},
			"11,-6": {{Q: 11, R: -7}, {Q: 10, R: -6}, {Q: 10, R: -5}},
			"12,-8": {{Q: 12, R: -9}, {Q: 11, R: -8}, {Q: 11, R: -7}},
			"5,-4":  {{Q: 6, R: -4}, {Q: 6, R: -5}, {Q: 5, R: -5}, {Q: 4, R: -4}, {Q: 4, R: -3}, {Q: 5, R: -3}},
		},
	},
}

// GetLevelHexMapConfigByID 根据ID获取关卡专用的六边形地图配置
func GetLevelHexMapConfigByID(hexMapID int) *HexMapConfig {
	config, exists := levelHexMapConfigs[hexMapID]
	if !exists {
		logx.Warnf("关卡六边形地图ID不存在，使用默认配置 hexMapID:%d", hexMapID)
		// 返回第一个可用的配置作为默认
		for _, defaultConfig := range levelHexMapConfigs {
			return defaultConfig
		}
		return nil
	}

	// HTML工具已经生成了完整的邻居关系，不需要自动计算
	// 但如果邻居关系为空，仍然可以自动计算作为备用
	if len(config.NeighborMap) == 0 {
		logx.Infof("邻居关系为空，自动计算 hexMapID:%d", hexMapID)
		config.NeighborMap = calculateHexNeighbors(config.ValidHexes)
	}

	return config
}

// calculateHexNeighbors 计算六边形邻居关系
func calculateHexNeighbors(validHexes []HexCoord) map[string][]HexCoord {
	neighborMap := make(map[string][]HexCoord)
	validSet := make(map[string]bool)

	// 创建有效坐标集合
	for _, hex := range validHexes {
		key := fmt.Sprintf("%d,%d", hex.Q, hex.R)
		validSet[key] = true
	}

	// 六边形的6个邻居方向
	directions := []HexCoord{
		{Q: 1, R: 0}, {Q: 1, R: -1}, {Q: 0, R: -1},
		{Q: -1, R: 0}, {Q: -1, R: 1}, {Q: 0, R: 1},
	}

	// 为每个有效坐标计算邻居
	for _, hex := range validHexes {
		key := fmt.Sprintf("%d,%d", hex.Q, hex.R)
		neighbors := make([]HexCoord, 0)

		for _, dir := range directions {
			neighborQ := hex.Q + dir.Q
			neighborR := hex.R + dir.R
			neighborKey := fmt.Sprintf("%d,%d", neighborQ, neighborR)

			if validSet[neighborKey] {
				neighbors = append(neighbors, HexCoord{Q: neighborQ, R: neighborR})
			}
		}

		neighborMap[key] = neighbors
	}

	return neighborMap
}
