package roommgr

import (
	"minesweep/common/logx"
	"minesweep/constvar"
)

// DisplayPhaseHandler 展示阶段处理器
type DisplayPhaseHandler struct {
	initialized bool // 标记是否已经初始化（执行过展示逻辑）
}

// GetPhaseName 获取阶段名称
func (h *DisplayPhaseHandler) GetPhaseName() string {
	return "Display"
}

// Enter 进入展示阶段
func (h *DisplayPhaseHandler) Enter(room *Room) error {
	// 无论通过什么方式进入展示阶段，都重新设置为完整的展示时间
	room.countDown = MinesweeperShowTime // 5秒
	room.gameStatus = constvar.GameStatusMinesweeping

	// 只在第一次进入时执行展示逻辑
	if !h.initialized {
		logx.Infof("展示阶段初始化开始 RoomID:%v Round:%d", room.RoomID, room.currentRound)

		// 执行展示阶段的核心逻辑
		if err := h.executeDisplayLogic(room); err != nil {
			return err
		}

		h.initialized = true
		logx.Infof("展示阶段初始化完成 RoomID:%v Round:%d", room.RoomID, room.currentRound)
	}

	logx.Infof("进入展示阶段 RoomID:%v Round:%d CountDown:%d",
		room.RoomID, room.currentRound, room.countDown)

	return nil
}

// Tick 展示阶段的每秒处理
func (h *DisplayPhaseHandler) Tick(room *Room) error {
	// 倒计时减1
	room.countDown--

	logx.Debugf("展示阶段倒计时 RoomID:%v Round:%d CountDown:%d",
		room.RoomID, room.currentRound, room.countDown)

	// 检查是否展示时间结束
	if room.countDown <= 0 {
		logx.Infof("展示阶段结束，转换到回合结束阶段 RoomID:%v Round:%d",
			room.RoomID, room.currentRound)
		return room.phaseManager.TransitionTo(PhaseRoundEnd)
	}

	return nil
}

// Exit 退出展示阶段
func (h *DisplayPhaseHandler) Exit(room *Room) error {
	// 重置初始化标志，为下一回合准备
	h.initialized = false

	logx.Infof("退出展示阶段 RoomID:%v Round:%d", room.RoomID, room.currentRound)
	return nil
}

// CanTransitionTo 检查是否可以转换到指定阶段
func (h *DisplayPhaseHandler) CanTransitionTo(newPhase RoomPhase) bool {
	switch newPhase {
	case PhaseRoundEnd:
		return true // 展示阶段可以转换到回合结束阶段
	case PhaseGameEnd:
		return true // 任何阶段都可以直接转换到游戏结束（异常情况）
	default:
		return false
	}
}

// executeDisplayLogic 执行展示阶段的核心逻辑
func (h *DisplayPhaseHandler) executeDisplayLogic(room *Room) error {
	logx.Infof("执行展示阶段逻辑 RoomID:%v Round:%d", room.RoomID, room.currentRound)

	// 1. 为未操作的玩家执行AI托管（使用统一的AI托管管理器）
	room.executeAIOperationsForManagedUsers()

	// 2. 更新地图状态（必须在计算得分之前）
	room.updateMapWithPlayerActions()

	// 3. 计算回合得分
	room.calculateRoundScores()

	// 4. 更新踩雷统计
	room.updateMineHitStatistics()

	// 5. 检查游戏结束条件（为前端提前准备结束动画）
	isGameEnded := room.checkGameEndConditions()

	// 6. 广播操作展示（包含游戏结束状态）
	room.broadcastActionDisplay(isGameEnded)

	logx.Infof("展示阶段逻辑执行完成 RoomID:%v Round:%d GameEnded:%v", room.RoomID, room.currentRound, isGameEnded)
	return nil
}
