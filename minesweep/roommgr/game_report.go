package roommgr

import (
	"context"
	"encoding/json"
	"fmt"
	"minesweep/common/convertx"
	"minesweep/common/logx"
	"minesweep/common/platform"
	"minesweep/common/redisx"
	"minesweep/common/safe"
	"minesweep/common/tools"
	"minesweep/conf"
	"minesweep/constvar"
	"minesweep/model/common/base"
	"time"

	"ms-version.soofun.online/wjl/game_public/external"
	"ms-version.soofun.online/wjl/game_public/types_public"
)

// ReportGameStart 上报游戏开始
func (slf *Room) ReportGameStart() {
	safe.Go(func() {
		var rUid, rToken string
		allUser := slf.GetAllUser()
		for _, v := range allUser {
			if v.IsRobot() || len(v.SSToken) == 0 {
				continue
			}

			if len(rUid) == 0 {
				rUid = v.UserID
				rToken = v.SSToken
				break
			}
		}
		if len(rUid) == 0 {
			logx.Errorf("游戏开始上报失败，未找到上报用户 RoomID:%v", slf.RoomID)
		}

		var players = make([]external.PlayerStartInfo, 0)
		for _, v := range allUser {
			if v.UserStatus != constvar.UserStatusSit {
				continue
			}

			players = append(players, external.PlayerStartInfo{
				UserID: v.UserID,
				IsAI:   convertx.BoolToInt(v.IsRobot()), // 1 机器人 ,0 真人
				Bet:    slf.fee,
			})
		}

		extendBytes, _ := json.Marshal(&base.ReportExtend{
			Extend: slf.apiCreateExtend,
			Users:  make([]*base.ReportUser, 0),
		})
		reportData := &external.GameStartInfo{
			GameID:       conf.Conf.Server.GameId,
			GameRoundID:  slf.GetRoundID(),
			CurrencyType: 0,
			StartAt:      slf.CreateTime.UnixMilli(),
			Players:      players,
			Extend:       string(extendBytes),
		}
		if slf.voiceRoom != nil {
			reportData.RoomID = slf.voiceRoom.GetVoiceRoomId()
		}
		resp, err := platform.ZegoGame().ReportGameStart(types_public.BobiEnv(conf.Conf.Server.Env), slf.appID, rUid, rToken, reportData)
		logx.Infof("游戏开始上报完成 RoomID:%v, reportData:%v, resp:%v, err:%v", slf.RoomID, tools.GetObj(reportData), tools.GetObj(resp), err)
		// 上报信息写入redis
		//slf.cacheGameStatus(1, reportData)
	})
}

// ReportGameSettle 上报游戏结束
func (slf *Room) ReportGameSettle(start, end int64, roundID string, endInfoList []base.GameEndInfo, extend *base.ReportExtend) {
	go func(start, end int64, roundID string, gameEndInfo []base.GameEndInfo) {
		defer safe.RecoverPanic()
		var rUid, rToken string
		for _, v := range gameEndInfo {
			if v.IsAI == 1 || len(v.SSToken) == 0 {
				continue
			}

			if len(rUid) == 0 {
				rUid = v.UserID
				rToken = v.SSToken
				break
			}
		}
		if len(rUid) == 0 {
			logx.Errorf("RoomID:%v ReportGameSettle no find reportUser", slf.RoomID)
		}

		var players = make([]external.PlayerResultInfo, 0)
		for _, v := range gameEndInfo {
			players = append(players, external.PlayerResultInfo{
				UserID: v.UserID,
				IsAI:   v.IsAI,
				Rank:   v.Rank,
				Reward: v.Reward,
				Score:  v.Score,
			})
		}
		extendBytes, _ := json.Marshal(extend)
		reportData := &external.GameSettleInfo{
			GameID:       conf.Conf.Server.GameId,
			GameRoundID:  slf.GetRoundID(),
			CurrencyType: 0,
			StartAt:      slf.CreateTime.UnixMilli(),
			EndAt:        end,
			Players:      players,
			Extend:       string(extendBytes),
		}
		if slf.voiceRoom != nil {
			reportData.RoomID = slf.voiceRoom.GetVoiceRoomId()
		}

		resp, err := platform.ZegoGame().ReportGameSettle(types_public.BobiEnv(conf.Conf.Server.Env), slf.appID, rUid, rToken, reportData)
		logx.Infof("RoomID:%v ReportGameSettle reportData:%v, resp:%v, err:%v", slf.RoomID, tools.GetObj(reportData), tools.GetObj(resp), err)

		// 上报信息写入redis
		//slf.cacheGameStatus(2, reportData)
	}(start, end, roundID, endInfoList)
}

// 上报信息写入redis(imo、likee渠道专用)
func (slf *Room) cacheGameStatus(status int, data interface{}) {
	if slf.appChannel != "imo" && slf.appChannel != "likee" && slf.appChannel != "debug" {
		return
	}

	type (
		// VoiceRoomStatus redis缓存的上报内容
		VoiceRoomStatus struct {
			Status int         `json:"status"` // 1 游戏开始,2 游戏结束
			Data   interface{} `json:"data"`
		}

		// RoomStatusInfo 通过redis发布订阅功能, 通知其他balance服务器
		RoomStatusInfo struct {
			Key    string      `json:"key"`
			Expire int64       `json:"expire"`
			Status int         `json:"status"`
			Data   interface{} `json:"data"`
		}
	)

	statusData := &VoiceRoomStatus{
		Status: status,
		Data:   data,
	}
	gameRoundId := slf.GetRoundID()
	dataBytes, _ := json.Marshal(statusData)
	key := fmt.Sprintf("voiceroom:%v:%v:%v:%v", slf.appChannel, slf.appID, conf.Conf.Server.GameId, gameRoundId)
	redisx.GetClient().Set(context.TODO(), key, string(dataBytes), 3*time.Hour)
	logx.Infof("RoomID:%v cacheGameStatus key:%v, status:%v, data:%+v", slf.RoomID, key, status, data)

	if slf.voiceRoom != nil {
		// 设置语聊房对应的
		key2 := fmt.Sprintf("platformroom:%v:%v:%v", slf.appChannel, conf.Conf.Server.GameId, slf.voiceRoom.GetVoiceRoomId())
		if status == 1 {
			redisx.GetClient().Set(context.TODO(), key2, gameRoundId, 3*time.Hour)
			logx.Infof("RoomID:%v cacheGameStatus key2:%v set gameRoundId:%v", slf.RoomID, key2, gameRoundId)
		} else {
			redisx.GetClient().Del(context.TODO(), key2)
			logx.Infof("RoomID:%v cacheGameStatus key2:%v delete", slf.RoomID, key2)
		}
	}
	statusInfo := &RoomStatusInfo{
		Key:    key,
		Expire: time.Now().Unix() + 3*60*60,
		Status: status,
		Data:   data,
	}
	statusBytes, _ := json.Marshal(statusInfo)
	redisx.GetClient().Publish(context.TODO(), "likeeRoomStatus", string(statusBytes))
}
