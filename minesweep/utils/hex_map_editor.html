<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>六边形地图配置工具</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* 自定义样式 */
        .hex-canvas {
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            cursor: crosshair;
            background: white;
        }
        
        .coord-item {
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }
        
        .mine-input {
            background-color: #fef3c7;
            border-color: #fbbf24;
        }
        
        .mine-input:focus {
            border-color: #f59e0b;
            box-shadow: 0 0 0 3px rgba(245, 158, 11, 0.1);
        }
        
        /* Toast 通知样式 */
        .toast {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            min-width: 300px;
            transform: translateX(100%);
            transition: transform 0.3s ease-in-out;
        }
        
        .toast.show {
            transform: translateX(0);
        }
        
        .toast.success {
            background: #10b981;
            color: white;
        }
        
        .toast.error {
            background: #ef4444;
            color: white;
        }
        
        .toast.info {
            background: #3b82f6;
            color: white;
        }
        
        /* 滚动条样式 */
        .custom-scrollbar::-webkit-scrollbar {
            width: 6px;
        }
        
        .custom-scrollbar::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 3px;
        }
        
        .custom-scrollbar::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 3px;
        }
        
        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Toast 容器 -->
    <div id="toastContainer"></div>

    <div class="min-h-screen p-4">
        <div class="max-w-7xl mx-auto">
            <!-- 头部 -->
            <div class="bg-blue-600 text-white rounded-lg p-6 mb-6 text-center">
                <h1 class="text-3xl font-bold mb-2">🎮 六边形地图设计工具</h1>
                <p class="text-blue-100">点击六边形来选择/取消选择，设计你的地图形状，生成Go语言配置</p>
            </div>

            <!-- 工具栏 -->
            <div class="bg-white rounded-lg shadow-sm border p-4 mb-6 flex flex-wrap gap-3 items-center">
                <button onclick="clearAll()" class="px-3 py-1.5 text-sm border border-gray-300 rounded hover:bg-gray-50 transition-colors">
                    清空地图
                </button>
                <button onclick="loadDefaultTemplate()" class="px-3 py-1.5 text-sm border border-gray-300 rounded hover:bg-gray-50 transition-colors">
                    默认模板
                </button>
                <button onclick="undo()" id="undoBtn" class="px-3 py-1.5 text-sm border border-gray-300 rounded hover:bg-gray-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed">
                    撤销
                </button>
                <button onclick="toggleOriginMode()" id="originBtn" class="px-3 py-1.5 text-sm border border-gray-300 rounded hover:bg-gray-50 transition-colors">
                    设置原点
                </button>
                <button onclick="showImportDialog()" class="px-3 py-1.5 text-sm border border-gray-300 rounded hover:bg-gray-50 transition-colors">
                    导入配置
                </button>
                
                <div class="flex flex-wrap gap-3 items-center ml-auto">
                    <div class="flex items-center gap-2">
                        <span class="text-sm font-medium">地图ID:</span>
                        <input type="number" id="mapId" value="1" class="w-16 h-8 px-2 text-sm border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    <div class="flex items-center gap-2">
                        <span class="text-sm font-medium">地图名:</span>
                        <input type="text" id="mapName" value="自定义地图" class="w-32 h-8 px-2 text-sm border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    <div class="flex items-center gap-2">
                        <span class="text-sm font-medium">地雷数:</span>
                        <input type="number" id="customMineCount" placeholder="自动" class="w-16 h-8 px-2 text-sm border rounded mine-input focus:outline-none">
                    </div>
                </div>
            </div>

            <!-- 主要内容 -->
            <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
                <!-- 画布区域 -->
                <div class="lg:col-span-3">
                    <div class="bg-white rounded-lg shadow-sm border">
                        <div class="p-6">
                            <div class="flex justify-center items-center bg-gray-50 rounded-lg p-4">
                                <canvas id="hexCanvas" width="600" height="600" class="hex-canvas"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 侧边栏 -->
                <div class="space-y-6">
                    <!-- 地图信息 -->
                    <div class="bg-white rounded-lg shadow-sm border">
                        <div class="p-4">
                            <h3 class="text-lg font-semibold mb-4">📊 地图信息</h3>
                            <div class="space-y-3">
                                <div class="flex justify-between items-center">
                                    <span class="text-sm">已选择:</span>
                                    <span class="px-2 py-1 text-xs bg-gray-100 rounded" id="selectedCount">0 个六边形</span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-sm">建议地雷数:</span>
                                    <span class="px-2 py-1 text-xs bg-gray-100 rounded" id="suggestedMines">0 个</span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-sm">实际地雷数:</span>
                                    <span class="px-2 py-1 text-xs rounded" id="actualMines">0 个</span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-sm">当前坐标:</span>
                                    <span class="text-sm font-mono" id="currentCoord">-</span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-sm">原点位置:</span>
                                    <span class="text-sm font-mono" id="originInfo">(0, 0)</span>
                                </div>

                                <!-- 导入配置信息 -->
                                <div id="importedConfigInfo" class="mt-4 p-3 bg-green-50 border border-green-200 rounded-lg hidden">
                                    <div class="text-sm font-medium text-green-800 mb-2">📥 导入的配置信息:</div>
                                    <div class="text-xs text-green-700 space-y-1" id="importedDetails"></div>
                                </div>

                                <!-- 快速设置地雷数 -->
                                <div class="mt-4">
                                    <div class="text-sm font-medium mb-2">💣 快速设置地雷数:</div>
                                    <div class="flex flex-wrap gap-2">
                                        <button onclick="setMineCount(5)" class="px-2 py-1 text-xs border border-gray-300 rounded hover:bg-gray-50">5个</button>
                                        <button onclick="setMineCount(10)" class="px-2 py-1 text-xs border border-gray-300 rounded hover:bg-gray-50">10个</button>
                                        <button onclick="setMineCount(15)" class="px-2 py-1 text-xs border border-gray-300 rounded hover:bg-gray-50">15个</button>
                                        <button onclick="setMineCount(20)" class="px-2 py-1 text-xs border border-gray-300 rounded hover:bg-gray-50">20个</button>
                                        <button onclick="setMineCount('')" class="px-2 py-1 text-xs border border-yellow-300 bg-yellow-50 rounded hover:bg-yellow-100">自动</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 坐标列表 -->
                    <div class="bg-white rounded-lg shadow-sm border">
                        <div class="p-4">
                            <h3 class="text-lg font-semibold mb-4">📍 坐标列表</h3>
                            <div class="h-[200px] overflow-y-auto custom-scrollbar">
                                <div class="bg-gray-50 rounded border p-3">
                                    <div id="coordList" class="coord-item text-gray-500 text-center">暂无选择的坐标</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 导出配置 -->
                    <div class="bg-white rounded-lg shadow-sm border">
                        <div class="p-4">
                            <h3 class="text-lg font-semibold mb-4">📤 导出配置</h3>
                            <div class="space-y-4">
                                <div class="grid grid-cols-2 gap-2">
                                    <button onclick="exportConfig('go')" class="px-3 py-1.5 text-sm border border-gray-300 rounded hover:bg-gray-50 transition-colors">
                                        生成Go配置
                                    </button>
                                    <button onclick="exportConfig('json')" class="px-3 py-1.5 text-sm border border-gray-300 rounded hover:bg-gray-50 transition-colors">
                                        生成JSON
                                    </button>
                                </div>
                                <textarea id="configOutput" placeholder="生成的配置将显示在这里..." class="w-full h-[150px] p-2 text-xs border border-gray-300 rounded font-mono resize-none focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
                                <button onclick="copyToClipboard()" class="w-full px-3 py-1.5 text-sm border border-gray-300 rounded hover:bg-gray-50 transition-colors">
                                    复制到剪贴板
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 导入配置对话框 -->
    <div id="importDialog" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
        <div class="bg-white rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[80vh] overflow-y-auto">
            <h3 class="text-lg font-semibold mb-4">📥 导入Go配置</h3>
            <p class="text-sm text-gray-600 mb-4">请粘贴Go配置代码到下面的文本框中：</p>
            <textarea id="importTextarea" placeholder="请粘贴Go配置代码..." class="w-full h-[300px] p-3 text-xs border border-gray-300 rounded font-mono resize-none focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
            <div class="flex justify-end gap-2 mt-4">
                <button onclick="closeImportDialog()" class="px-4 py-2 text-sm border border-gray-300 rounded hover:bg-gray-50 transition-colors">
                    取消
                </button>
                <button onclick="importConfig()" class="px-4 py-2 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors">
                    导入
                </button>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        const canvas = document.getElementById('hexCanvas');
        const ctx = canvas.getContext('2d');
        let selectedHexes = new Set();
        let history = [];
        let hoveredHex = null;
        let isOriginMode = false;
        let originOffset = { q: 0, r: 0 };
        let importedConfig = null;

        // 六边形参数
        const HEX_SIZE = 20;
        const HEX_WIDTH = HEX_SIZE * 2;
        const HEX_HEIGHT = Math.sqrt(3) * HEX_SIZE;
        const GRID_RANGE = 8;

        // 颜色配置
        const COLORS = {
            grid: '#e0e0e0',
            selected: '#4CAF50',
            hover: '#81C784',
            text: '#333',
            background: '#fafafa',
            origin: '#FF5722',
            originStroke: '#D84315'
        };

        // 初始化
        function init() {
            loadDefaultTemplate();
            setupEventListeners();
            updateUI();
        }

        // 设置事件监听器
        function setupEventListeners() {
            canvas.addEventListener('click', handleClick);
            canvas.addEventListener('mousemove', handleMouseMove);
            canvas.addEventListener('mouseleave', handleMouseLeave);
            document.getElementById('customMineCount').addEventListener('input', updateUI);
            
            // 点击对话框外部关闭
            document.getElementById('importDialog').addEventListener('click', function(e) {
                if (e.target === this) {
                    closeImportDialog();
                }
            });
        }

        // 六边形坐标转换
        function hexToPixel(q, r) {
            const x = HEX_SIZE * (Math.sqrt(3) * q + Math.sqrt(3) / 2 * r);
            const y = HEX_SIZE * (3 / 2 * r);
            return {
                x: x + canvas.width / 2,
                y: y + canvas.height / 2
            };
        }

        function pixelToHex(x, y) {
            const relX = x - canvas.width / 2;
            const relY = y - canvas.height / 2;
            const q = (Math.sqrt(3) / 3 * relX - 1 / 3 * relY) / HEX_SIZE;
            const r = (2 / 3 * relY) / HEX_SIZE;
            return hexRound(q, r);
        }

        function hexRound(q, r) {
            const s = -q - r;
            let rq = Math.round(q);
            let rr = Math.round(r);
            let rs = Math.round(s);

            const qDiff = Math.abs(rq - q);
            const rDiff = Math.abs(rr - r);
            const sDiff = Math.abs(rs - s);

            if (qDiff > rDiff && qDiff > sDiff) {
                rq = -rr - rs;
            } else if (rDiff > sDiff) {
                rr = -rq - rs;
            }

            return { q: rq, r: rr };
        }

        // 绘制六边形
        function drawHexagon(centerX, centerY, size, fillColor, strokeColor) {
            ctx.beginPath();
            for (let i = 0; i < 6; i++) {
                const angle = (Math.PI / 3) * i + Math.PI / 6;
                const x = centerX + size * Math.cos(angle);
                const y = centerY + size * Math.sin(angle);
                if (i === 0) {
                    ctx.moveTo(x, y);
                } else {
                    ctx.lineTo(x, y);
                }
            }
            ctx.closePath();
            if (fillColor) {
                ctx.fillStyle = fillColor;
                ctx.fill();
            }
            if (strokeColor) {
                ctx.strokeStyle = strokeColor;
                ctx.lineWidth = 1;
                ctx.stroke();
            }
        }

        // 绘制网格
        function drawGrid() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            for (let q = -GRID_RANGE; q <= GRID_RANGE; q++) {
                for (let r = -GRID_RANGE; r <= GRID_RANGE; r++) {
                    if (Math.abs(q + r) > GRID_RANGE) continue;

                    const pixel = hexToPixel(q, r);
                    const coordKey = `${q},${r}`;

                    let fillColor = null;
                    let strokeColor = COLORS.grid;

                    // 检查是否是原点
                    if (q === originOffset.q && r === originOffset.r) {
                        fillColor = COLORS.origin;
                        strokeColor = COLORS.originStroke;
                    } else if (selectedHexes.has(coordKey)) {
                        fillColor = COLORS.selected;
                        strokeColor = '#2E7D32';
                    } else if (hoveredHex && hoveredHex.q === q && hoveredHex.r === r) {
                        if (isOriginMode) {
                            fillColor = '#FFC107';
                            strokeColor = '#F57C00';
                        } else {
                            fillColor = COLORS.hover;
                        }
                    }

                    drawHexagon(pixel.x, pixel.y, HEX_SIZE, fillColor, strokeColor);

                    // 绘制坐标文本
                    const displayCoord = getDisplayCoord(q, r);
                    ctx.fillStyle = (q === originOffset.q && r === originOffset.r) ? 'white' : COLORS.text;
                    ctx.font = '10px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText(`${displayCoord.q},${displayCoord.r}`, pixel.x, pixel.y + 3);

                    // 在原点位置绘制"O"标记
                    if (q === originOffset.q && r === originOffset.r) {
                        ctx.fillStyle = 'white';
                        ctx.font = 'bold 14px Arial';
                        ctx.textAlign = 'center';
                        ctx.textBaseline = 'middle';
                        ctx.fillText('O', pixel.x, pixel.y - 8);
                    }
                }
            }
        }

        // 获取显示坐标
        function getDisplayCoord(internalQ, internalR) {
            return {
                q: internalQ - originOffset.q,
                r: internalR - originOffset.r
            };
        }

        // 获取内部坐标
        function getInternalCoord(displayQ, displayR) {
            return {
                q: displayQ + originOffset.q,
                r: displayR + originOffset.r
            };
        }

        // 处理点击事件
        function handleClick(event) {
            const rect = canvas.getBoundingClientRect();
            const x = event.clientX - rect.left;
            const y = event.clientY - rect.top;
            const hex = pixelToHex(x, y);

            if (isOriginMode) {
                setOrigin(hex.q, hex.r);
                return;
            }

            const coordKey = `${hex.q},${hex.r}`;
            
            // 保存历史状态
            history.push(new Set(selectedHexes));
            if (history.length > 50) history.shift();

            if (selectedHexes.has(coordKey)) {
                selectedHexes.delete(coordKey);
            } else {
                selectedHexes.add(coordKey);
            }

            updateUIAndRedraw();
        }

        // 处理鼠标移动
        function handleMouseMove(event) {
            const rect = canvas.getBoundingClientRect();
            const x = event.clientX - rect.left;
            const y = event.clientY - rect.top;
            const hex = pixelToHex(x, y);

            if (!hoveredHex || hoveredHex.q !== hex.q || hoveredHex.r !== hex.r) {
                hoveredHex = hex;
                const displayCoord = getDisplayCoord(hex.q, hex.r);
                document.getElementById('currentCoord').textContent = `(${displayCoord.q}, ${displayCoord.r})`;
                drawGrid();
            }
        }

        // 处理鼠标离开
        function handleMouseLeave() {
            hoveredHex = null;
            document.getElementById('currentCoord').textContent = '-';
            drawGrid();
        }

        // 设置原点
        function setOrigin(clickedQ, clickedR) {
            originOffset = { q: clickedQ, r: clickedR };
            isOriginMode = false;
            document.getElementById('originBtn').textContent = '设置原点';
            document.getElementById('originBtn').className = 'px-3 py-1.5 text-sm border border-gray-300 rounded hover:bg-gray-50 transition-colors';
            document.getElementById('originInfo').textContent = `(${clickedQ}, ${clickedR})`;
            updateUIAndRedraw();
            showToast('原点已设置', `已设置原点为 (${clickedQ}, ${clickedR})，所有坐标已重新计算`, 'success');
        }

        // 切换原点模式
        function toggleOriginMode() {
            isOriginMode = !isOriginMode;
            const btn = document.getElementById('originBtn');
            if (isOriginMode) {
                btn.textContent = '取消设置';
                btn.className = 'px-3 py-1.5 text-sm bg-red-600 text-white rounded hover:bg-red-700 transition-colors';
                showToast('设置原点模式', '点击任意六边形设置为原点(0,0)', 'info');
            } else {
                btn.textContent = '设置原点';
                btn.className = 'px-3 py-1.5 text-sm border border-gray-300 rounded hover:bg-gray-50 transition-colors';
                showToast('已退出原点设置模式', '', 'success');
            }
        }

        // 加载默认模板
        function loadDefaultTemplate() {
            history.push(new Set(selectedHexes));
            selectedHexes.clear();

            const defaultCoords = [
                [-3, 0], [-2, 0], [-1, 0], [0, 0], [1, 0], [2, 0], [3, 0],
                [-3, 1], [-2, 1], [-1, 1], [0, 1], [1, 1], [2, 1], [3, 1], [4, 1],
                [-4, 2], [-3, 2], [-2, 2], [-1, 2], [0, 2], [1, 2], [2, 2], [3, 2],
                [-4, 3], [-3, 3], [-2, 3], [-1, 3], [0, 3], [1, 3], [2, 3], [3, 3],
                [-4, 4], [-3, 4], [-2, 4], [-1, 4], [0, 4], [1, 4], [2, 4], [3, 4],
                [-3, 5], [-2, 5], [-1, 5], [0, 5], [1, 5], [2, 5], [3, 5]
            ];

            defaultCoords.forEach(([q, r]) => {
                selectedHexes.add(`${q},${r}`);
            });

            document.getElementById('mapName').value = '默认六边形地图';
            document.getElementById('customMineCount').value = '';
            clearImportedConfigDisplay();
            updateUIAndRedraw();
            showToast('模板已加载', '已加载默认六边形模板', 'success');
        }

        // 清空地图
        function clearAll() {
            history.push(new Set(selectedHexes));
            selectedHexes.clear();
            originOffset = { q: 0, r: 0 };
            document.getElementById('originInfo').textContent = '(0, 0)';
            document.getElementById('mapName').value = '自定义地图';
            document.getElementById('customMineCount').value = '';
            clearImportedConfigDisplay();
            updateUIAndRedraw();
            showToast('地图已清空', '地图已清空，原点已重置', 'success');
        }

        // 撤销
        function undo() {
            if (history.length > 0) {
                const lastState = history.pop();
                selectedHexes = new Set(lastState);
                updateUIAndRedraw();
                updateUndoButton();
                showToast('已撤销', '已撤销上一步操作', 'success');
            }
        }

        // 更新撤销按钮状态
        function updateUndoButton() {
            const undoBtn = document.getElementById('undoBtn');
            if (history.length === 0) {
                undoBtn.disabled = true;
                undoBtn.classList.add('disabled:opacity-50', 'disabled:cursor-not-allowed');
            } else {
                undoBtn.disabled = false;
                undoBtn.classList.remove('disabled:opacity-50', 'disabled:cursor-not-allowed');
            }
        }

        // 计算邻居关系
        function calculateNeighbors() {
            const directions = [
                {q: 1, r: 0}, {q: 1, r: -1}, {q: 0, r: -1},
                {q: -1, r: 0}, {q: -1, r: 1}, {q: 0, r: 1}
            ];

            const neighborMap = {};

            selectedHexes.forEach(coordKey => {
                const [q, r] = coordKey.split(',').map(Number);
                const displayCoord = getDisplayCoord(q, r);
                const displayKey = `${displayCoord.q},${displayCoord.r}`;
                const neighbors = [];

                directions.forEach(dir => {
                    const neighborQ = q + dir.q;
                    const neighborR = r + dir.r;
                    const neighborKey = `${neighborQ},${neighborR}`;
                    
                    if (selectedHexes.has(neighborKey)) {
                        const neighborDisplayCoord = getDisplayCoord(neighborQ, neighborR);
                        neighbors.push(neighborDisplayCoord);
                    }
                });

                neighborMap[displayKey] = neighbors;
            });

            return neighborMap;
        }

        // 导出配置
        function exportConfig(format) {
            if (selectedHexes.size === 0) {
                showToast('错误', '请先选择一些六边形', 'error');
                return;
            }

            const mapId = parseInt(document.getElementById('mapId').value);
            const mapName = document.getElementById('mapName').value;
            const suggestedMines = Math.max(1, Math.floor(selectedHexes.size * 0.35));
            const customMineCount = document.getElementById('customMineCount').value;
            const actualMines = customMineCount ? parseInt(customMineCount) : suggestedMines;

            if (actualMines >= selectedHexes.size) {
                showToast('错误', '地雷数量不能大于等于总格子数', 'error');
                return;
            }

            if (actualMines <= 0) {
                showToast('错误', '地雷数量必须大于0', 'error');
                return;
            }

            const coords = Array.from(selectedHexes)
                .map(coord => {
                    const [q, r] = coord.split(',').map(Number);
                    return getDisplayCoord(q, r);
                })
                .sort((a, b) => a.q - b.q || a.r - b.r);

            // 确保包含原点
            const hasOrigin = coords.some(coord => coord.q === 0 && coord.r === 0);
            if (!hasOrigin) {
                coords.push({ q: 0, r: 0 });
                coords.sort((a, b) => a.q - b.q || a.r - b.r);
            }

            if (format === 'go') {
                const coordsStr = coords.map(coord => `\t\t\t{Q: ${coord.q}, R: ${coord.r}}`).join(',\n');
                const neighborMap = calculateNeighbors();
                let neighborMapStr = '';
                
                if (Object.keys(neighborMap).length > 0) {
                    const neighborEntries = Object.entries(neighborMap)
                        .map(([key, neighbors]) => {
                            const neighborsStr = neighbors.map(n => `{Q: ${n.q}, R: ${n.r}}`).join(', ');
                            return `\t\t\t"${key}": {${neighborsStr}}`;
                        })
                        .join(',\n');
                    neighborMapStr = `,\n\t\tNeighborMap: map[string][]HexCoord{\n${neighborEntries},\n\t\t}`;
                }

                const goConfig = `\t${mapId}: {
\t\tMapID:      ${mapId},
\t\tMapName:    "${mapName}",
\t\tValidHexes: []HexCoord{
${coordsStr},
\t\t},
\t\tMineCount:  ${actualMines}${neighborMapStr},
\t},`;

                document.getElementById('configOutput').value = goConfig;
                showToast('Go配置已生成', `地雷数: ${actualMines}个，可直接复制到hexMapConfigs中`, 'success');
            } else {
                const neighborMap = calculateNeighbors();
                const jsonConfig = {
                    mapId,
                    mapName,
                    validHexes: coords,
                    suggestedMines,
                    actualMines,
                    neighborMap
                };

                document.getElementById('configOutput').value = JSON.stringify(jsonConfig, null, 2);
                showToast('JSON配置已生成', `地雷数: ${actualMines}个，坐标已根据设置的原点调整`, 'success');
            }
        }

        // 复制到剪贴板
        function copyToClipboard() {
            const output = document.getElementById('configOutput').value;
            if (!output) {
                showToast('错误', '请先生成配置', 'error');
                return;
            }

            navigator.clipboard.writeText(output).then(() => {
                showToast('已复制', '已复制到剪贴板', 'success');
            }).catch(() => {
                // 降级方案
                const textArea = document.createElement('textarea');
                textArea.value = output;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                showToast('已复制', '已复制到剪贴板', 'success');
            });
        }

        // 快速设置地雷数量
        function setMineCount(count) {
            const input = document.getElementById('customMineCount');
            input.value = count;
            updateUI();
            if (count === '') {
                showToast('已设置为自动', '已设置为自动计算地雷数量', 'success');
            } else {
                showToast('地雷数量已设置', `已设置地雷数量为 ${count} 个`, 'success');
            }
        }

        // 显示导入对话框
        function showImportDialog() {
            document.getElementById('importDialog').style.display = 'flex';
        }

        // 关闭导入对话框
        function closeImportDialog() {
            document.getElementById('importDialog').style.display = 'none';
            document.getElementById('importTextarea').value = '';
        }

        // 导入配置
        function importConfig() {
            const textarea = document.getElementById('importTextarea');
            const configText = textarea.value.trim();

            if (!configText) {
                showToast('错误', '请输入配置代码', 'error');
                return;
            }

            try {
                const configData = parseGoConfig(configText);
                
                if (configData.coords.length === 0) {
                    showToast('错误', '未找到有效的坐标数据', 'error');
                    return;
                }

                // 更新配置信息
                if (configData.mapId !== undefined) {
                    document.getElementById('mapId').value = configData.mapId;
                }
                if (configData.mapName !== undefined) {
                    document.getElementById('mapName').value = configData.mapName;
                }
                if (configData.mineCount !== undefined) {
                    document.getElementById('customMineCount').value = configData.mineCount;
                }

                // 保存导入的配置信息
                importedConfig = {
                    mapId: configData.mapId,
                    mapName: configData.mapName,
                    mineCount: configData.mineCount
                };
                updateImportedConfigDisplay(importedConfig);

                // 转换坐标并添加到选中集合
                selectedHexes.clear();
                configData.coords.forEach(coord => {
                    const internalCoord = getInternalCoord(coord.q, coord.r);
                    const key = `${internalCoord.q},${internalCoord.r}`;
                    selectedHexes.add(key);
                });

                updateUIAndRedraw();
                closeImportDialog();

                const importedInfo = [];
                if (configData.mapId !== undefined) importedInfo.push(`地图ID: ${configData.mapId}`);
                if (configData.mapName !== undefined) importedInfo.push(`地图名: ${configData.mapName}`);
                if (configData.mineCount !== undefined) importedInfo.push(`地雷数: ${configData.mineCount}`);

                showToast('导入成功', `成功导入 ${configData.coords.length} 个坐标${importedInfo.length > 0 ? ' | ' + importedInfo.join(', ') : ''}`, 'success');
            } catch (error) {
                showToast('导入失败', '配置格式错误，请检查输入', 'error');
            }
        }

        // 解析Go配置
        function parseGoConfig(configText) {
            const result = {
                coords: []
            };

            // 解析地图ID
            const mapIdMatch = configText.match(/MapID:\s*(\d+)/);
            if (mapIdMatch) {
                result.mapId = parseInt(mapIdMatch[1]);
            }

            // 解析地图名称
            const mapNameMatch = configText.match(/MapName:\s*"([^"]+)"/);
            if (mapNameMatch) {
                result.mapName = mapNameMatch[1];
            }

            // 解析地雷数量
            const mineCountMatch = configText.match(/MineCount:\s*(\d+)/);
            if (mineCountMatch) {
                result.mineCount = parseInt(mineCountMatch[1]);
            }

            // 解析坐标
            const coordMatches = configText.match(/\{Q:\s*(-?\d+),\s*R:\s*(-?\d+)\}/g);
            if (coordMatches) {
                coordMatches.forEach(match => {
                    const coordMatch = match.match(/\{Q:\s*(-?\d+),\s*R:\s*(-?\d+)\}/);
                    if (coordMatch) {
                        result.coords.push({
                            q: parseInt(coordMatch[1]),
                            r: parseInt(coordMatch[2])
                        });
                    }
                });
            }

            return result;
        }

        // 更新导入配置信息显示
        function updateImportedConfigDisplay(configData) {
            const infoPanel = document.getElementById('importedConfigInfo');
            const detailsElement = document.getElementById('importedDetails');
            const details = [];

            if (configData.mapId !== undefined) details.push(`🆔 地图ID: ${configData.mapId}`);
            if (configData.mapName !== undefined) details.push(`📝 地图名: ${configData.mapName}`);
            if (configData.mineCount !== undefined) details.push(`💣 地雷数: ${configData.mineCount}`);

            if (details.length > 0) {
                detailsElement.innerHTML = details.join('<br>');
                infoPanel.style.display = 'block';
            } else {
                infoPanel.style.display = 'none';
            }
        }

        // 清空导入配置信息显示
        function clearImportedConfigDisplay() {
            document.getElementById('importedConfigInfo').style.display = 'none';
            importedConfig = null;
        }

        // 更新UI
        function updateUI() {
            const count = selectedHexes.size;
            const suggestedMines = Math.max(1, Math.floor(count * 0.35));
            const customMineCount = document.getElementById('customMineCount').value;
            const actualMines = customMineCount ? parseInt(customMineCount) : suggestedMines;

            document.getElementById('selectedCount').textContent = `${count} 个六边形`;
            document.getElementById('suggestedMines').textContent = `${suggestedMines} 个`;
            document.getElementById('actualMines').textContent = `${actualMines} 个`;

            // 更新实际地雷数颜色
            const actualMinesElement = document.getElementById('actualMines');
            if (actualMines >= count && count > 0) {
                actualMinesElement.className = 'px-2 py-1 text-xs bg-red-100 text-red-800 rounded';
            } else {
                actualMinesElement.className = 'px-2 py-1 text-xs bg-gray-100 rounded';
            }

            // 更新坐标列表
            const coordList = document.getElementById('coordList');
            if (count === 0) {
                coordList.innerHTML = '<div class="text-gray-500 text-center">暂无选择的坐标</div>';
            } else {
                const coords = Array.from(selectedHexes)
                    .sort()
                    .map(coord => {
                        const [q, r] = coord.split(',').map(Number);
                        const displayCoord = getDisplayCoord(q, r);
                        return `<div class="coord-item">{q: ${displayCoord.q}, r: ${displayCoord.r}}</div>`;
                    })
                    .join('');
                coordList.innerHTML = coords;
            }

            updateUndoButton();
        }

        // 更新UI并重绘
        function updateUIAndRedraw() {
            updateUI();
            drawGrid();
        }

        // Toast 通知系统
        function showToast(title, message, type = 'info') {
            const toastContainer = document.getElementById('toastContainer');
            const toast = document.createElement('div');
            toast.className = `toast ${type} rounded-lg shadow-lg p-4 mb-2`;
            
            const content = `
                <div class="font-semibold">${title}</div>
                ${message ? `<div class="text-sm opacity-90">${message}</div>` : ''}
            `;
            
            toast.innerHTML = content;
            toastContainer.appendChild(toast);

            // 显示动画
            setTimeout(() => toast.classList.add('show'), 10);

            // 自动移除
            setTimeout(() => {
                toast.classList.remove('show');
                setTimeout(() => {
                    if (toast.parentNode) {
                        toast.parentNode.removeChild(toast);
                    }
                }, 300);
            }, 3000);
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', init);
    </script>
</body>
</html>