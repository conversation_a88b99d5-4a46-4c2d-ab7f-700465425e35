package pairmgr

import (
	"errors"
	"fmt"
	"minesweep/common/logx"
	"minesweep/common/platform"
	"minesweep/common/tools"
	"minesweep/common/warning"
	"minesweep/conf"
	"minesweep/constvar"
	"minesweep/ecode"
	"minesweep/localmgr"
	"minesweep/model/dao"
	"minesweep/roommgr"
	"minesweep/usermgr"
	"sync"
	"time"

	"github.com/go-redis/redis/v8"
	"ms-version.soofun.online/wjl/game_public/types_public"
)

type (
	Channel struct {
		sync.RWMutex
		AppChannel string
		AppID      int64
		PlayerNum  int
		Fee        int
		PairUsers  []*PairUser
	}

	PairUser struct {
		UserID    string    // 进入的匹配队列的玩家Id
		EnterTime time.Time // 进入匹配队列的时间
		WaitTime  time.Time // 最大等待时间,超过改时间没有匹配到玩家,就开始匹配机器人
	}
)

// canAddRobot 返回是否可以添加机器人
func (slf *Channel) canAddRobot() bool {
	channelCfg, _ := dao.GroupDao.ChannelConf.Get(slf.AppChannel, slf.AppID)
	if !channelCfg.NeedRobot {
		return false
	}

	curJackpot, err := dao.GroupDao.Jackpot.Get(slf.AppChannel, slf.AppID)
	if errors.Is(err, redis.Nil) {
		curJackpot = channelCfg.BaseJackpot
		_ = dao.GroupDao.Jackpot.Set(slf.AppChannel, slf.AppID, channelCfg.BaseJackpot)
	}

	// 计算盈余：yingyu = curJackpot - base_jackpot
	yingyu := curJackpot - channelCfg.BaseJackpot

	// 熔断检查：如果 (yingyu + fusing) < 0 不添加机器人
	if (yingyu + channelCfg.Fusing) < 0 {
		logx.Infof("canAddRobot forbidAddRobot, AppChannel:%v, Yingyu:%v, BaseJackpot:%v, CurJackpot:%v, Fusing:%v",
			slf.AppChannel, yingyu, channelCfg.BaseJackpot, curJackpot, channelCfg.Fusing)
		msg := fmt.Sprintf("游戏名称:扫雷, 游戏id:%v, Channel:%v, 机器人熔断报警,不能添加机器人, 盈余:%v, 基础奖池:%v, 当前奖池:%v, 熔断值:%v",
			conf.Conf.Server.GameId, slf.AppChannel, yingyu, channelCfg.BaseJackpot, curJackpot, channelCfg.Fusing)
		warning.SendWarning(warning.WarningInfo{"预警详情": msg})
		return false
	}

	return true
}

// IsInPair 玩家是否在匹配队列中
func (slf *Channel) IsInPair(userID string) bool {
	slf.RLock()
	defer slf.RUnlock()

	for _, v := range slf.PairUsers {
		if v.UserID == userID {
			return true
		}
	}
	return false
}

// CancelPair 取消匹配
func (slf *Channel) CancelPair(userID string) bool {
	slf.Lock()
	defer slf.Unlock()

	for i := len(slf.PairUsers) - 1; i >= 0; i-- {
		if slf.PairUsers[i].UserID == userID {
			slf.PairUsers = append(slf.PairUsers[:i], slf.PairUsers[i+1:]...)
			logx.Infof("用户取消匹配成功 userID:%v, pairUsersLen:%v", userID, len(slf.PairUsers))
		}
	}
	return true
}

// AddUser 玩家进入匹配队列
func (slf *Channel) AddUser(userID string, wait *conf.PairWait) {
	slf.Lock()
	defer slf.Unlock()

	slf.PairUsers = append(slf.PairUsers, &PairUser{
		UserID:    userID,
		EnterTime: time.Now(),
		WaitTime:  time.Now().Add(time.Second * time.Duration(tools.Rand(wait.Min, wait.Max))),
	})
	logx.Infof("用户匹配成功 userID:%v, pairUsersLen:%v", userID, len(slf.PairUsers))
}

// AutoMatch 自动匹配
func (slf *Channel) AutoMatch() {
	slf.Lock()
	defer slf.Unlock()

	if slf.PlayerNum <= 0 {
		return
	}

	for {
		if len(slf.PairUsers) < slf.PlayerNum {
			break
		}

		pairUsers := slf.PairUsers[:slf.PlayerNum]
		slf.PairUsers = slf.PairUsers[slf.PlayerNum:]

		var users []*usermgr.User
		var validList []*PairUser
		for _, v := range pairUsers {
			user := usermgr.GetInstance().GetUserById(slf.AppChannel, slf.AppID, v.UserID)
			if user != nil {
				users = append(users, user)
				validList = append(validList, v)
			}
		}

		if len(users) != slf.PlayerNum {
			slf.PairUsers = append(slf.PairUsers, validList...)
			continue
		}

		slf.pairSuccess(users)
	}

	if len(slf.PairUsers) == 0 || !slf.canAddRobot() {
		return
	}

	// 检测剩余玩家是否有等待超时的
	needRobot := false
	for _, user := range slf.PairUsers {
		if time.Now().After(user.WaitTime) {
			needRobot = true
			break
		}
	}
	if !needRobot {
		return
	}

	var users []*usermgr.User
	for _, v := range slf.PairUsers {
		user := usermgr.GetInstance().GetUserById(slf.AppChannel, slf.AppID, v.UserID)
		if user != nil {
			users = append(users, user)
		}
	}
	if len(users) == 0 {
		return
	}

	// 获取缺少的机器人，并清空等待匹配的队列
	var robotCount = slf.PlayerNum - len(users)
	robots := platform.GetRandRobot(slf.AppChannel, slf.AppID, robotCount)
	if len(robots) != robotCount {
		return
	}
	slf.PairUsers = []*PairUser{}

	// 机器人数据转为用户，执行匹配成功处理
	for _, v := range robots {
		users = append(users, &usermgr.User{
			AppID:      slf.AppID,
			AppChannel: slf.AppChannel,
			UserID:     v.UserID,
			Nickname:   v.Nickname,
			Avatar:     v.Avatar,
			IsRobot:    true,
			AllProduct: make(map[constvar.ProductID]bool),
		})
	}
	slf.pairSuccess(users)
}

// pairSuccess 匹配成功
func (slf *Channel) pairSuccess(users []*usermgr.User) {
	channelCfg, _ := dao.GroupDao.ChannelConf.Get(slf.AppChannel, slf.AppID)

	// 创建新房间
	room := roommgr.GetInstance().CreateRoom(slf.AppChannel, slf.AppID, slf.PlayerNum, slf.Fee, constvar.RoomTypeCommon)
	if room == nil {
		logx.Errorf("pairSuccess CreateRoom failed, users:%+v", users)
		for _, v := range users {
			localmgr.GetInstance().RmvLocal(v.AppChannel, v.AppID, v.UserID)
		}
		return
	}

	// 扣除所有玩家的入场费(包括机器人)
	var feeUsers = make(map[string]*usermgr.User) // 已扣除手续费的邀请玩家
	var failedUserID string                       // 导致本次匹配失败的用户ID
	for _, user := range users {
		// 扣除入场费
		var coinChg = -slf.Fee
		_, errCode := user.ChangeBalance(int(types_public.ActionEventOne), int64(coinChg), "bet", room.GetRoundID(), "bet", channelCfg.CoinType, constvar.CoinChangeTypeRoomFee, constvar.RoomTypeCommon)
		if errCode != ecode.OK {
			failedUserID = user.UserID
			localmgr.GetInstance().RmvLocal(user.AppChannel, user.AppID, user.UserID)
			logx.Errorf("pairSuccess ChangeBalance failed userID:%v, coinChg:%v, errCode:%v", user.UserID, coinChg, errCode)
			user.SendMessage(constvar.MsgTypePairResult, errCode, struct{}{})
			break
		}
		feeUsers[user.UserID] = user
	}

	// 有人扣除入场费失败，返还入场费，解散房间
	if len(feeUsers) != slf.PlayerNum {
		for _, user := range feeUsers {
			// 返还入场费
			var coinChg = slf.Fee
			_, errCode := user.ChangeBalance(int(types_public.ActionEventTwo), int64(coinChg), "bet", room.GetRoundID(), "bet", channelCfg.CoinType, constvar.CoinChangeTypeBackRoomFee, constvar.RoomTypeCommon)
			if errCode != ecode.OK {
				logx.Errorf("pairSuccess ChangeBalance failed backRoomFee userID:%v, coinChg:%v, errCode:%v", user.UserID, coinChg, errCode)
				continue
			}
		}

		// 再次放进匹配队列(导致匹配失败的玩家除外)
		for _, user := range users {
			if user.UserID == failedUserID {
				continue
			}
			slf.PairUsers = append(slf.PairUsers, &PairUser{
				UserID:    user.UserID,
				EnterTime: time.Now(),
				WaitTime:  time.Now().Add(time.Second * time.Duration(tools.Rand(2, 4))),
			})
		}
		roommgr.GetInstance().RemoveRoom(room.RoomID)
		return
	}

	// 玩家进入房间，开始游戏
	for i, v := range users {
		user, ok := feeUsers[v.UserID]
		if !ok {
			logx.Errorf("pairSuccess no find userID:%v", v.UserID)
			return
		}

		var identityType = constvar.IdentityTypeUser
		if user.IsRobot {
			identityType = constvar.IdentityTypeRobot
		}
		room.UserJoin(&roommgr.RoomUser{
			NickName:     user.Nickname,
			Avatar:       user.Avatar,
			UserID:       user.UserID,
			Coin:         user.Coin,
			Pos:          i,
			SSToken:      user.SSToken,
			ClientIp:     user.ClientIP,
			PlatRoomID:   user.PlatRoomID,
			IdentityType: identityType,
			SkinChessID:  user.SkinChessID,
		})
		if !user.IsRobot {
			localmgr.GetInstance().SetLocal(slf.AppChannel, slf.AppID, user.UserID, &localmgr.Local{RoomID: room.RoomID, FreshTime: time.Now().Unix()})
		}
	}
	// 开始游戏
	room.Start()
}

// removeAll 删除所有待匹配
func (slf *Channel) removeAll() {
	slf.Lock()
	defer slf.Unlock()

	for _, v := range slf.PairUsers {
		localmgr.GetInstance().RmvLocal(slf.AppChannel, slf.AppID, v.UserID)
	}
	slf.PairUsers = make([]*PairUser, 0)
}
