package model

import (
	"fmt"
	"minesweep/common/gormx"
	"time"

	"gorm.io/gorm"
)

type (
	// LevelConfig 关卡配置（支持六边形特殊关卡）
	LevelConfig struct {
		ID         uint      `gorm:"column:Id;primary_key;comment:主键ID"`
		LevelID    int       `gorm:"column:LevelID;type:int;not null;unique;index;comment:关卡编号(1-30)"`  // 关卡ID (1-30)
		MapType    int       `gorm:"column:MapType;type:tinyint;not null;comment:地图类型(0=方格地图,1=六边形地图)"` // 地图类型 (0=方格, 1=六边形)
		MapWidth   int       `gorm:"column:MapWidth;type:int;default:0;comment:地图宽度(仅方格地图使用)"`          // 地图宽度 (方格地图用)
		MapHeight  int       `gorm:"column:MapHeight;type:int;default:0;comment:地图高度(仅方格地图使用)"`         // 地图高度 (方格地图用)
		HexMapID   int       `gorm:"column:HexMapID;type:int;default:0;comment:六边形地图配置ID(仅六边形地图使用)"`    // 六边形地图ID (六边形地图用)
		MineCount  int       `gorm:"column:MineCount;type:int;not null;comment:地雷数量"`                   // 地雷数量
		IsSpecial  bool      `gorm:"column:IsSpecial;type:tinyint;default:0;comment:是否特殊关卡(0=普通,1=特殊)"` // 是否特殊关卡
		IsActive   bool      `gorm:"column:IsActive;type:tinyint;default:1;comment:是否激活(0=禁用,1=启用)"`    // 是否激活
		CreateTime time.Time `gorm:"column:CreateTime;type:datetime;comment:创建时间"`                      // 创建时间
		UpdateTime time.Time `gorm:"column:UpdateTime;type:datetime;comment:更新时间"`                      // 更新时间
	}

	// LevelConfigSearch 关卡配置查询
	LevelConfigSearch struct {
		Orm *gorm.DB
		LevelConfig
		Page
		Order string
	}

	// LevelMapInfo 关卡地图信息 (API返回用)
	LevelMapInfo struct {
		LevelID     int                    `json:"levelId"`               // 关卡ID
		MapType     int                    `json:"mapType"`               // 地图类型 (0=方格, 1=六边形)
		MapWidth    int                    `json:"mapWidth,omitempty"`    // 地图宽度 (方格地图用)
		MapHeight   int                    `json:"mapHeight,omitempty"`   // 地图高度 (方格地图用)
		ValidHexes  []*HexCoord            `json:"validHexes,omitempty"`  // 六边形有效坐标
		NeighborMap map[string][]*HexCoord `json:"neighborMap,omitempty"` // 六边形邻居关系
		MineCount   int                    `json:"mineCount"`             // 地雷数量
		IsSpecial   bool                   `json:"isSpecial"`             // 是否特殊关卡
		IsUnlocked  bool                   `json:"isUnlocked"`            // 是否已解锁
		IsCleared   bool                   `json:"isCleared"`             // 是否已通关
	}

	// HexCoord 六边形坐标
	HexCoord struct {
		Q int `json:"q"`
		R int `json:"r"`
	}
)

// TableName 指定表名
func (*LevelConfig) TableName() string {
	return "level_config"
}

func NewLevelConfigSearch() *LevelConfigSearch {
	return &LevelConfigSearch{Orm: gormx.GetDB()}
}

func (slf *LevelConfigSearch) SetOrm(tx *gorm.DB) *LevelConfigSearch {
	slf.Orm = tx
	return slf
}

func (slf *LevelConfigSearch) SetPage(page, size int) *LevelConfigSearch {
	slf.PageNum, slf.PageSize = page, size
	return slf
}

func (slf *LevelConfigSearch) SetOrder(order string) *LevelConfigSearch {
	slf.Order = order
	return slf
}

func (slf *LevelConfigSearch) SetLevelID(levelID int) *LevelConfigSearch {
	slf.LevelID = levelID
	return slf
}

func (slf *LevelConfigSearch) SetMapType(mapType int) *LevelConfigSearch {
	slf.MapType = mapType
	return slf
}

func (slf *LevelConfigSearch) SetHexMapID(hexMapID int) *LevelConfigSearch {
	slf.HexMapID = hexMapID
	return slf
}

func (slf *LevelConfigSearch) SetIsSpecial(isSpecial bool) *LevelConfigSearch {
	slf.IsSpecial = isSpecial
	return slf
}

func (slf *LevelConfigSearch) SetIsActive(isActive bool) *LevelConfigSearch {
	slf.IsActive = isActive
	return slf
}

// Create 单条插入
func (slf *LevelConfigSearch) Create(recordM *LevelConfig) (uint, error) {
	recordM.CreateTime = time.Now()
	recordM.UpdateTime = time.Now()
	if err := slf.Orm.Create(recordM).Error; err != nil {
		return 0, fmt.Errorf("create recordM err: %v, recordM: %+v", err, recordM)
	}
	return recordM.ID, nil
}

// CreateBatch 批量插入
func (slf *LevelConfigSearch) CreateBatch(records []*LevelConfig) error {
	now := time.Now()
	for _, record := range records {
		record.CreateTime = now
		record.UpdateTime = now
	}
	if err := slf.Orm.Create(&records).Error; err != nil {
		return fmt.Errorf("create records err: %v, records: %+v", err, records)
	}
	return nil
}

// build 构建条件
func (slf *LevelConfigSearch) build() *gorm.DB {
	var db = slf.Orm.Table(slf.TableName()).Model(LevelConfig{})

	if slf.LevelID > 0 {
		db = db.Where("LevelID = ?", slf.LevelID)
	}

	if slf.MapType > 0 {
		db = db.Where("MapType = ?", slf.MapType)
	}

	if slf.HexMapID > 0 {
		db = db.Where("HexMapID = ?", slf.HexMapID)
	}

	if slf.IsSpecial {
		db = db.Where("IsSpecial = ?", slf.IsSpecial)
	}

	db = db.Where("IsActive = ?", slf.IsActive)

	if slf.Order != "" {
		db = db.Order(slf.Order)
	} else {
		db = db.Order("LevelID ASC")
	}

	return db
}

// Find 多条查询
func (slf *LevelConfigSearch) Find() ([]*LevelConfig, int64, error) {
	var (
		records = make([]*LevelConfig, 0)
		total   int64
		db      = slf.build()
	)

	if err := db.Count(&total).Error; err != nil {
		return records, total, fmt.Errorf("find count err: %v", err)
	}
	if slf.PageNum*slf.PageSize > 0 {
		db = db.Offset((slf.PageNum - 1) * slf.PageSize).Limit(slf.PageSize)
	}
	if err := db.Find(&records).Error; err != nil {
		return records, total, fmt.Errorf("find records err: %v", err)
	}

	return records, total, nil
}

// First 单条查询
func (slf *LevelConfigSearch) First() (*LevelConfig, error) {
	var (
		recordM = new(LevelConfig)
		db      = slf.build()
	)

	if err := db.First(recordM).Error; err != nil {
		return recordM, err
	}

	return recordM, nil
}

// UpdateByMap 更新
func (slf *LevelConfigSearch) UpdateByMap(upMap map[string]interface{}) error {
	upMap["UpdateTime"] = time.Now()
	var db = slf.build()

	if err := db.Updates(upMap).Error; err != nil {
		return fmt.Errorf("update by map err: %v, upMap: %+v", err, upMap)
	}

	return nil
}

// GetAllActiveLevels 获取所有激活的关卡
func (slf *LevelConfigSearch) GetAllActiveLevels() ([]*LevelConfig, error) {
	slf.SetIsActive(true)
	records, _, err := slf.Find()
	return records, err
}
