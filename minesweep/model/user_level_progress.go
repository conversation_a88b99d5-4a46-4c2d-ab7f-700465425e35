package model

import (
	"fmt"
	"minesweep/common/gormx"
	"time"

	"gorm.io/gorm"
)

// UserLevelProgress 用户关卡进度
type UserLevelProgress struct {
	ID                  uint       `gorm:"column:Id;primary_key;comment:主键ID" json:"id"`
	AppChannel          string     `gorm:"column:AppChannel;type:varchar(50);not null;default:'';comment:应用渠道" json:"appChannel"`
	AppID               int64      `gorm:"column:AppID;not null;default:0;comment:应用ID" json:"appId"`
	UserID              string     `gorm:"column:UserID;type:varchar(50);not null;default:'';comment:用户ID" json:"userId"`
	HighestClearedLevel int        `gorm:"column:HighestClearedLevel;type:int;not null;default:0;comment:最高通关关卡" json:"highestClearedLevel"` // 最高通关关卡
	TotalClearedCount   int        `gorm:"column:TotalClearedCount;type:int;not null;default:0;comment:总通关数量" json:"totalClearedCount"`      // 总通关数量
	LastClearTime       *time.Time `gorm:"column:LastClearTime;type:datetime;default:null;comment:最后通关时间" json:"lastClearTime"`              // 最后通关时间
	CreateTime          time.Time  `gorm:"column:CreateTime;type:datetime;comment:创建时间" json:"createTime"`
	UpdateTime          time.Time  `gorm:"column:UpdateTime;type:datetime;comment:更新时间" json:"updateTime"`
}

// TableName 表名
func (UserLevelProgress) TableName() string {
	return "user_level_progress"
}

// UserLevelProgressSearch 用户关卡进度查询
type UserLevelProgressSearch struct {
	Orm        *gorm.DB
	AppChannel string
	AppID      int64
	UserID     string
	PageNum    int
	PageSize   int
}

// NewUserLevelProgressSearch 创建查询实例
func NewUserLevelProgressSearch() *UserLevelProgressSearch {
	return &UserLevelProgressSearch{
		Orm: gormx.GetDB(),
	}
}

// SetAppChannel 设置应用渠道
func (slf *UserLevelProgressSearch) SetAppChannel(appChannel string) *UserLevelProgressSearch {
	slf.AppChannel = appChannel
	return slf
}

// SetAppID 设置应用ID
func (slf *UserLevelProgressSearch) SetAppID(appID int64) *UserLevelProgressSearch {
	slf.AppID = appID
	return slf
}

// SetUserID 设置用户ID
func (slf *UserLevelProgressSearch) SetUserID(userID string) *UserLevelProgressSearch {
	slf.UserID = userID
	return slf
}

// SetPageNum 设置页码
func (slf *UserLevelProgressSearch) SetPageNum(pageNum int) *UserLevelProgressSearch {
	slf.PageNum = pageNum
	return slf
}

// SetPageSize 设置页大小
func (slf *UserLevelProgressSearch) SetPageSize(pageSize int) *UserLevelProgressSearch {
	slf.PageSize = pageSize
	return slf
}

// build 构建查询条件
func (slf *UserLevelProgressSearch) build() *gorm.DB {
	db := slf.Orm.Model(&UserLevelProgress{})

	if slf.AppChannel != "" {
		db = db.Where("AppChannel = ?", slf.AppChannel)
	}
	if slf.AppID > 0 {
		db = db.Where("AppID = ?", slf.AppID)
	}
	if slf.UserID != "" {
		db = db.Where("UserID = ?", slf.UserID)
	}

	return db
}

// First 查询单条记录
func (slf *UserLevelProgressSearch) First() (*UserLevelProgress, error) {
	var record UserLevelProgress
	db := slf.build()

	if err := db.First(&record).Error; err != nil {
		return nil, err
	}

	return &record, nil
}

// Find 查询多条记录
func (slf *UserLevelProgressSearch) Find() ([]*UserLevelProgress, int64, error) {
	var (
		records = make([]*UserLevelProgress, 0)
		total   int64
		db      = slf.build()
	)

	if err := db.Count(&total).Error; err != nil {
		return records, total, fmt.Errorf("find count err: %v", err)
	}

	if slf.PageNum*slf.PageSize > 0 {
		db = db.Offset((slf.PageNum - 1) * slf.PageSize).Limit(slf.PageSize)
	}

	if err := db.Find(&records).Error; err != nil {
		return records, total, fmt.Errorf("find records err: %v", err)
	}

	return records, total, nil
}

// Create 创建记录
func (slf *UserLevelProgressSearch) Create(record *UserLevelProgress) (*UserLevelProgress, error) {
	record.CreateTime = time.Now()
	record.UpdateTime = time.Now()

	if err := slf.Orm.Create(record).Error; err != nil {
		return nil, fmt.Errorf("create record err: %v", err)
	}

	return record, nil
}

// UpdateByMap 更新记录
func (slf *UserLevelProgressSearch) UpdateByMap(upMap map[string]interface{}) error {
	upMap["UpdateTime"] = time.Now()
	db := slf.build()

	if err := db.Updates(upMap).Error; err != nil {
		return fmt.Errorf("update by map err: %v, upMap: %+v", err, upMap)
	}

	return nil
}

// CreateOrUpdate 创建或更新用户进度
func (slf *UserLevelProgressSearch) CreateOrUpdate(appChannel string, appID int64, userID string, levelID int) error {
	// 查询现有记录
	existing, err := slf.SetAppChannel(appChannel).SetAppID(appID).SetUserID(userID).First()

	now := time.Now()

	if err != nil {
		// 记录不存在，创建新记录
		newRecord := &UserLevelProgress{
			AppChannel:          appChannel,
			AppID:               appID,
			UserID:              userID,
			HighestClearedLevel: levelID,
			TotalClearedCount:   1,
			LastClearTime:       &now,
			CreateTime:          now,
			UpdateTime:          now,
		}

		_, err = slf.Create(newRecord)
		return err
	}

	// 记录存在，更新进度
	updateMap := map[string]interface{}{
		"HighestClearedLevel": levelID,
		"TotalClearedCount":   existing.TotalClearedCount + 1,
		"LastClearTime":       now,
	}

	return slf.SetAppChannel(appChannel).SetAppID(appID).SetUserID(userID).UpdateByMap(updateMap)
}

// GetUserProgress 获取用户关卡进度
func (slf *UserLevelProgressSearch) GetUserProgress(appChannel string, appID int64, userID string) (int, int, int, error) {
	progress, err := slf.SetAppChannel(appChannel).SetAppID(appID).SetUserID(userID).First()

	if err != nil {
		// 新用户，返回默认值
		return 30, 0, 1, nil
	}

	totalLevels := 30
	clearedLevels := progress.TotalClearedCount
	currentLevel := progress.HighestClearedLevel + 1

	// 确保当前关卡不超过总关卡数
	if currentLevel > totalLevels {
		currentLevel = totalLevels
	}

	return totalLevels, clearedLevels, currentLevel, nil
}
