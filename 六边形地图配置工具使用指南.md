# 🎮 六边形地图设计工具使用指南

## 🎯 **工具概述**

这是一个简化的六边形地图设计工具，专门用于手动设计扫雷游戏的六边形地图。你可以根据地图图片，逐个点击六边形来精确设计地图形状。

### **工具特点**
- ✅ **手动设计**：点击选择六边形，精确控制地图形状
- ✅ **实时预览**：立即看到地图效果和统计信息
- ✅ **JSON导出**：生成标准JSON配置，直接集成到代码
- ✅ **撤销功能**：支持操作撤销
- ✅ **简洁界面**：专注于核心设计功能

## 🚀 **使用流程**

### **步骤1：准备工作**
1. 准备你的地图设计图片
2. 用浏览器打开 `hex_map_editor.html`
3. 看到六边形网格和工具栏（已预选默认模板）

### **步骤2：设计地图**
1. **使用默认模板**：工具已预选一个标准六边形区域作为起点
2. **加载默认模板**：点击"默认模板"按钮重新加载标准形状
3. **手动调整**：
   - 点击六边形选择/取消选择
   - 对照你的地图图片进行精确调整
   - 使用"撤销"回到上一步

### **步骤3：设置原点（可选）**
1. **点击"设置原点"按钮**：进入原点设置模式
2. **点击目标六边形**：将该位置设为坐标原点(0,0)
3. **自动调整坐标**：所有其他位置的坐标会相对于新原点重新计算
4. **原点标记**：原点位置会显示为橙色并标记"O"

### **步骤4：设置地图信息**
1. **设置地图ID**：右上角输入唯一的地图ID（1-6）
2. **设置地图名**：输入描述性的地图名称
3. **查看统计**：右侧显示选择的六边形数量和建议地雷数

### **步骤5：导出配置**
1. **生成Go配置**：点击"生成Go配置"按钮，直接生成Go语言格式
2. **生成JSON**：点击"生成JSON"按钮，生成JSON格式（可选）
3. **复制配置**：复制生成的Go配置，直接粘贴到hexMapConfigs中

## 📋 **生成的Go配置格式**

工具会直接生成符合Go语言语法的配置：

```go
1: {
    MapID:      1,
    MapName:    "自定义地图",
    ValidHexes: []HexCoord{
        {Q: -2, R: 0},
        {Q: -1, R: 0},
        {Q: 0, R: 0},
        {Q: 1, R: 0},
        {Q: 2, R: 0},
    },
    MineCount:  2,
    NeighborMap: map[string][]HexCoord{
        "-2,0": {{Q: -1, R: 0}},
        "-1,0": {{Q: -2, R: 0}, {Q: 0, R: 0}},
        "0,0": {{Q: -1, R: 0}, {Q: 1, R: 0}},
        "1,0": {{Q: 0, R: 0}, {Q: 2, R: 0}},
        "2,0": {{Q: 1, R: 0}},
    },
},
```

## 🔧 **直接集成到Go代码**

将生成的配置直接复制到 `minesweep/roommgr/minemap.go` 的 `hexMapConfigs` 中：

```go
var hexMapConfigs = map[int]*HexMapConfig{
    // 现有配置...

    // 直接粘贴工具生成的配置
    1: {
        MapID:      1,
        MapName:    "自定义地图",
        ValidHexes: []HexCoord{
            {Q: -2, R: 0}, {Q: -1, R: 0}, {Q: 0, R: 0},
            {Q: 1, R: 0}, {Q: 2, R: 0},
        },
        MineCount:  2,
        NeighborMap: map[string][]HexCoord{
            "-2,0": {{Q: -1, R: 0}},
            "-1,0": {{Q: -2, R: 0}, {Q: 0, R: 0}},
            "0,0": {{Q: -1, R: 0}, {Q: 1, R: 0}},
            "1,0": {{Q: 0, R: 0}, {Q: 2, R: 0}},
            "2,0": {{Q: 1, R: 0}},
        },
    },
}
```

## 💡 **设计建议**

### **6张地图设计思路**
1. **地图1：新手入门** - 简单形状，15-20个格子，25%地雷密度
2. **地图2：标准体验** - 经典六边形，30-37个格子，35%地雷密度
3. **地图3：十字挑战** - 十字形，20-25个格子，30%地雷密度
4. **地图4：创意形状** - 特殊形状，25-35个格子，35%地雷密度
5. **地图5：高难度** - 复杂形状，40-50个格子，40%地雷密度
6. **地图6：终极挑战** - 最复杂设计，50-60个格子，45%地雷密度

### **设计原则**
- **连通性**：确保所有六边形相互连接
- **平衡性**：避免过于复杂或简单的形状
- **独特性**：每张地图有不同特色
- **渐进性**：难度逐步递增

## 🔍 **注意事项**

### **原点设置功能**

#### **功能说明**
- **目的**：调整坐标系，让地图的关键位置成为原点(0,0)
- **效果**：所有坐标会相对于新原点重新计算
- **标记**：原点位置显示为橙色，并标记"O"

#### **使用方法**
1. 点击"设置原点"按钮，进入原点设置模式
2. 鼠标悬停的六边形会显示为黄色
3. 点击目标六边形，将其设为新的原点(0,0)
4. 所有坐标自动重新计算并更新显示

#### **应用场景**
- **地图中心**：将地图的中心位置设为原点
- **关键位置**：将游戏中的重要位置设为原点
- **坐标优化**：让大部分坐标为正数，便于理解

### **默认模板功能**

#### **预设模板**
- **自动加载**：打开工具时自动加载标准六边形模板
- **快速重置**：点击"默认模板"按钮快速加载标准形状
- **设计起点**：46个六边形组成的标准六边形区域

#### **模板特点**
- **标准形状**：类似蜂窝的六边形排列
- **合理大小**：46个格子，建议16个地雷
- **易于调整**：可以在此基础上增减格子

### **使用技巧**
1. **从模板开始**：使用默认模板作为设计起点
2. **对照图片**：准备地图设计图片，对照进行精确调整
3. **分步设计**：先调整整体形状，再精修细节
4. **设置原点**：在完成地图设计后，选择合适的原点位置
5. **实时检查**：随时查看右侧统计信息
6. **及时保存**：完成后立即复制Go配置

### **常见问题**
- **无法选择六边形**：检查鼠标点击位置是否准确
- **配置生成失败**：确保至少选择了一个六边形
- **复制失败**：手动选择文本框内容进行复制
- **原点设置失效**：确保点击的是有效的六边形位置

---

通过这个简化的工具，你可以精确地根据地图图片设计六边形地图，快速生成JSON配置并集成到游戏中！
